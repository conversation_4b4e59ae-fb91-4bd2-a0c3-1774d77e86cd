LicenseVerifySwitch = true

version=`git rev-parse --abbrev-ref HEAD | grep -v HEAD || git describe --exact-match HEAD || git rev-parse HEAD`
foeye_flags="-s -w -X 'api/internal.LicenseVerifySwitch=${LicenseVerifySwitch}' -X 'api/internal.Version=${version}' -X 'api/internal.BuildAt=${buildAt}' -X 'api/internal.Private=${PRIVATE_FOEYE_LICENSE_KEY}' -X 'api/internal.SHA265=${PRIVATE_FOEYE_LICENSE_SHA265}'"
foradar_flags="-s -w -X 'api/internal.LicenseVerifySwitch=${LicenseVerifySwitch}' -X 'api/internal.Version=${version}' -X 'api/internal.BuildAt=${buildAt}' -X 'api/internal.Private=${PRIVATE_FOEYE_LICENSE_KEY}' -X 'api/internal.SHA265=${PRIVATE_FORADAR_LICENSE_SHA265}'"

build_foeye:
	cd cmd/gateway && \
	GOOS=linux CGO_ENABLE=0 go build --tags foeye -ldflags=${foeye_flags} -o api

build_foradar:
	cd cmd/gateway && \
	GOOS=linux CGO_ENABLE=0 go build --tags foradar -ldflags=${foradar_flags} -o api
	
# 默认构建，不指定标签
build:
	cd cmd/gateway && \
	GOOS=linux CGO_ENABLE=0 go build -ldflags=${foradar_flags} -o api
docker:
	docker build -t api:latest .
