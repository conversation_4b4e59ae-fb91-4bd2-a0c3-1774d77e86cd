package main

import (
	"crawler/internal/config"
	"crawler/internal/handler"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	_ "github.com/go-micro/plugins/v4/broker/nats"
	_ "github.com/go-micro/plugins/v4/broker/rabbitmq"
	_ "github.com/go-micro/plugins/v4/broker/redis"
	grpcc "github.com/go-micro/plugins/v4/client/grpc"
	_ "github.com/go-micro/plugins/v4/registry/consul"
	_ "github.com/go-micro/plugins/v4/registry/etcd"
	_ "github.com/go-micro/plugins/v4/registry/nats"
	grpcs "github.com/go-micro/plugins/v4/server/grpc"
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/server"
)

var (
	Version string
	BuildAt string
)

func main() {
	fmt.Println("Version: ", Version)
	fmt.Println("BuildAt: ", BuildAt)

	var srv micro.Service
	// Create service
	srv = micro.NewService(
		micro.Server(grpcs.NewServer()),
		micro.Client(grpcc.NewClient()),
	)

	srv.Init(
		micro.Name(constant.ServiceCrawler),
		micro.Version(Version),
	)

	cc := config.ReadConfig()

	if cc == nil {
		logger.Fatal("the config file is empty")
	}

	if len(cc.NodeID) > 0 {
		_ = srv.Server().Init(server.Id(cc.NodeID))
	}

	crawler := handler.NewCrawler(cc, srv)

	// Register handler
	if err := rpcx.RegisterCrawlerHandler(srv.Server(), &crawler); err != nil {
		logger.Fatal(err)
	}

	// RegisterSubscriber
	if err := micro.RegisterSubscriber(srv.Server().Options().Name, srv.Server(), crawler.HandleEvent, server.SubscriberQueue(srv.Server().Options().Name)); err != nil {
		logger.Fatal(err)
	}

	// Run service
	if err := srv.Run(); err != nil {
		logger.Fatal(err)
	}
}
