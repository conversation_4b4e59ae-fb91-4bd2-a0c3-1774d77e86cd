[注册中心]

```go
// go.mod中引入git.gobies.org/shared-platform/foscan/pkg

srv := micro.NewService(
    //micro.Server(grpcs.NewServer()),/*必须使用rpc的server，不能使用grpc*/
    micro.Client(grpcc.NewClient()),
    micro.Registry(registry.NewMemoryRegistry()), // 使用内存注册中心
)

// 在启动本地注册中心的模块中，增加一个注册handler监听
if err := pb.RegisterRegistryHandler(srv.Server(), &handler.Registry{ID: srv.Server().Options().Id}); err != nil {
    logger.Fatal(err)
}
```

[其他服务]
```go
// 其他服务中，将micro.service的默认注册中心，选择为client连接远程的注册中心
srv := micro.NewService(
    micro.Server(grpcs.NewServer()), 
    micro.Client(grpcc.NewClient()),
    micro.Registry(client.NewRegistry()),  // 增加这个注册中心
)
```