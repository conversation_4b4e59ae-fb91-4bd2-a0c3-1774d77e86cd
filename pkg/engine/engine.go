package engine

import "sync"

type Engine struct {
	ID     string                     // 引擎ID
	Name   string                     // 引擎名称
	Attrs  map[string]string          // 引擎的属性
	Status int                        // 引擎状态，1: Available  2: Unavailable 9: Admin_Down
	tasks  map[string]map[string]*Job // 引擎正在处理的任务Job
	mutex  sync.Mutex
}

func NewEngine(name string) *Engine {
	return &Engine{
		Name:  name,
		Attrs: make(map[string]string),
		tasks: make(map[string]map[string]*Job),
	}
}

// AppendAttr 追加引擎属性
func (e *Engine) AppendAttr(key, value string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.Attrs[key] = value
}

// RemoveAttr 删除引擎属性
func (e *Engine) RemoveAttr(key string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	delete(e.Attrs, key)
}

// CreateJob 创建任务JOB
func (e *Engine) CreateJob(taskID, jobID string, job *Job) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	if jobs, exists := e.tasks[taskID]; !exists {
		e.tasks[taskID] = make(map[string]*Job)
		e.tasks[taskID][jobID] = job
	} else {
		if _, ok := jobs[jobID]; !ok {
			jobs[jobID] = job
		}
	}
}

// DeleteJob 清除任务Job
func (e *Engine) DeleteJob(taskID, jobID string) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	if len(e.tasks[taskID]) > 1 {
		delete(e.tasks[taskID], jobID)
	} else {
		delete(e.tasks, taskID)
	}
}

func (e *Engine) GetJobCount() int {
	var count int
	for _, jobs := range e.tasks {
		for _, _ = range jobs {
			count++
		}
	}
	return count
}
