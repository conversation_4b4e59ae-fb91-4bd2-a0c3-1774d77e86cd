package clues

import (
	"context"
	"crypto/tls"
	"fmt"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/black_keyword_system"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net"
	"regexp"
	"strings"
	"time"

	"github.com/jpillora/go-tld"
	"github.com/spf13/cast"
	"golang.org/x/net/idna"
)

// 定义常量
const (
	AssetsLevelA = 1
	AssetsLevelB = 2
	AssetsLevelC = 3
	AssetsLevelD = 4

	CacheDuration = 24 * time.Hour

	DomainSimilarValue = 80
)

// ATTRIBUTE_MAP 线索类型映射到FOFA查询前缀
var ATTRIBUTE_MAP = map[int]string{
	clues.TYPE_DOMAIN:    "domain",
	clues.TYPE_CERT:      "cert",
	clues.TYPE_ICP:       "icp",
	clues.TYPE_LOGO:      "icon_hash",
	clues.TYPE_KEYWORD:   "title",
	clues.TYPE_SUBDOMAIN: "host",
	clues.TYPE_IP:        "ip",
	clues.TYPE_FID:       "fid",
}

// 威胁类型
const (
	THREATEN_TYPE_OTHER            = iota //其他
	THREATEN_TYPE_DY                      //钓鱼
	THREATEN_TYPE_HDD                     //黄赌毒网站
	THREATEN_TYPE_FM_ICP                  //仿冒icp
	THREATEN_TYPE_DOMAIN_CONFUSION        //域名混淆
)

// LevelValue 表示级别值的结构
type LevelValue struct {
	Level int
	Val   LevelReason
}
type LevelReason struct {
	Level    int    `json:"level"`
	IsFake   bool   `json:"is_fake"`
	Reason   string `json:"reason"`
	FakeType int    `json:"fake_type"`
}

// LevelItem 表示级别项的结构
type LevelItem struct {
	IP LevelValue
}

// ClassifyAssetsLevel 资产分类主函数
func ClassifyAssetsLevel(asset *recommend_result.RecommendResult, clueList []*clues.Clue, userID uint64, key interface{}, total interface{}) error {
	log.Debugf("判定资产level调用-开始,ip:%s,user_id:%d,key:%v,total:%v,assets_id:%s", asset.Ip, userID, key, total, asset.Id)

	// 针对特定IPs的详细跟踪
	// targetIPs := []string{"*************", "*************"}
	// isTargetIP := false
	// for _, targetIP := range targetIPs {
	// 	if asset.Ip == targetIP {
	// 		isTargetIP = true
	// 		break
	// 	}
	// }
	// if isTargetIP {
	// 	log.Infof("【IP跟踪】ClassifyAssetsLevel开始处理目标IP - ip: %s, asset_id: %s, user_id: %d, url: %s, subdomain: %s",
	// 		asset.Ip, asset.Id, userID, asset.Url, asset.Subdomain)
	// 	log.Infof("【IP跟踪】线索数据统计 - 总线索数: %d", len(clueList))

	// 	// 统计各类型线索数量
	// 	ipClueCount := 0
	// 	domainClueCount := 0
	// 	subdomainClueCount := 0
	// 	certClueCount := 0
	// 	for _, clue := range clueList {
	// 		switch clue.Type {
	// 		case clues.TYPE_IP:
	// 			ipClueCount++
	// 		case clues.TYPE_DOMAIN:
	// 			domainClueCount++
	// 		case clues.TYPE_SUBDOMAIN:
	// 			subdomainClueCount++
	// 		case clues.TYPE_CERT:
	// 			certClueCount++
	// 		}
	// 	}
	// 	log.Infof("【IP跟踪】线索类型统计 - IP线索: %d, 域名线索: %d, 子域名线索: %d, 证书线索: %d",
	// 		ipClueCount, domainClueCount, subdomainClueCount, certClueCount)
	// }

	// 从缓存获取已有的级别
	cacheKey := cache.GetCacheKey("classifyAssetsLevel", fmt.Sprintf("%d_%s_level", userID, asset.Ip))
	var levelArr []LevelItem
	_ = redis.GetCache(cacheKey, &levelArr)

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】缓存检查 - cache_key: %s, 已有级别数: %d", cacheKey, len(levelArr))
	// 	if len(levelArr) > 0 {
	// 		for i, level := range levelArr {
	// 			log.Infof("【IP跟踪】已有级别[%d] - level: %d, is_fake: %v, reason: %s",
	// 				i, level.IP.Level, level.IP.Val.IsFake, level.IP.Val.Reason)
	// 		}
	// 	}
	// }

	// 1. 检查IP段
	ipClues := utils.StructFilter(clueList, map[string]interface{}{
		"Type":      clues.TYPE_IP,
		"IsDeleted": clues.NOT_DELETE,
		"Status":    clues.CLUE_PASS_STATUS,
	})

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】开始IP段检查 - 过滤后IP线索数: %d", len(ipClues))
	// 	for i, ipClue := range ipClues {
	// 		log.Infof("【IP跟踪】IP线索[%d] - content: %s, is_fake_icp: %d", i, ipClue.Content, ipClue.IsFakeIcp)
	// 	}
	// }

	for _, ipClue := range ipClues {
		// if isTargetIP {
		// 	log.Infof("【IP跟踪】检查IP是否在范围内 - target_ip: %s, clue_content: %s", asset.Ip, ipClue.Content)
		// }

		if isIPInRange(asset.Ip, ipClue.Content) {
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】IP匹配成功 - ip: %s, 匹配的线索: %s", asset.Ip, ipClue.Content)
			// }

			cl := findClueByContent(clueList, ipClue.Content)
			if cl.IsFakeIcp == 1 {
				if !hasLevel(levelArr, AssetsLevelD) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelD,
							Val:   LevelReason{Level: AssetsLevelD, IsFake: true, Reason: "ICP盗用"},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】IP段匹配，判定为D级(ICP盗用) - ip: %s", asset.Ip)
				// }
			} else {
				if !hasLevel(levelArr, AssetsLevelA) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val:   LevelReason{Level: AssetsLevelA, IsFake: false, Reason: "已知IP/IP段-" + ipClue.Content},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】IP段匹配，判定为A级 - ip: %s, 匹配线索: %s", asset.Ip, ipClue.Content)
				// }
			}
			// 保存到缓存
			ok := redis.SetCache(cacheKey, CacheDuration, levelArr)
			if !ok {
				log.Errorf("资产分类 - 保存缓存失败")
			}
			log.Infof("ip段判断，判定level为A,ip在线索的ip段里面,ip:%s,assets_id:%s,isFakeIcp:%v", asset.Ip, asset.Id, cl.IsFakeIcp)
			return nil
		}
	}

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】IP段检查完成，未找到匹配的IP段 - ip: %s", asset.Ip)
	// }

	// 2. 检查域名
	domainClues := utils.StructFilter(clueList, map[string]interface{}{
		"Type":      clues.TYPE_DOMAIN,
		"IsDeleted": clues.NOT_DELETE,
		"Status":    clues.CLUE_PASS_STATUS,
	})
	url := asset.Url
	if strings.Contains(url, "://") {
		parts := strings.Split(url, "://")
		if len(parts) > 1 {
			url = strings.Split(parts[1], ":")[0]
		}
	} else {
		url = strings.Split(url, ":")[0]
	}
	topDomain := GetTopDomain(url)

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】开始域名检查 - 过滤后域名线索数: %d, asset_url: %s, 提取的url: %s, top_domain: %s",
	// 		len(domainClues), asset.Url, url, topDomain)
	// 	for i, domainClue := range domainClues {
	// 		log.Infof("【IP跟踪】域名线索[%d] - content: %s, is_fake_icp: %d", i, domainClue.Content, domainClue.IsFakeIcp)
	// 	}
	// }

	for _, domainClue := range domainClues {
		// if isTargetIP {
		// 	log.Infof("【IP跟踪】检查域名匹配 - top_domain: %s, clue_content: %s", topDomain, domainClue.Content)
		// }

		if topDomain != "" && topDomain == domainClue.Content {
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】域名匹配成功 - ip: %s, 匹配的域名: %s", asset.Ip, domainClue.Content)
			// }

			cl := findClueByContent(clueList, domainClue.Content)
			if cl.IsFakeIcp == 1 {
				if !hasLevel(levelArr, AssetsLevelD) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelD,
							Val:   LevelReason{Level: AssetsLevelD, IsFake: true, Reason: "ICP盗用"},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】域名匹配，判定为D级(ICP盗用) - ip: %s", asset.Ip)
				// }
			} else {
				if !hasLevel(levelArr, AssetsLevelA) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val:   LevelReason{Level: AssetsLevelA, IsFake: false, Reason: "存在域名-" + domainClue.Content + " && " + domainClue.Content + "在企业线索库"},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】域名匹配，判定为A级 - ip: %s, 匹配域名: %s", asset.Ip, domainClue.Content)
				// }
			}

			// 保存到缓存
			ok := redis.SetCache(cacheKey, CacheDuration, levelArr)
			if !ok {
				log.Errorf("资产分类 - 保存缓存失败")
			}
			log.Debugf("根域名判断，判定level为A,ip:%s,assets_id:%s,isFakeIcp:%v", asset.Ip, asset.Id, cl.IsFakeIcp)
			return nil
		}
	}

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】域名检查完成，未找到匹配的域名 - ip: %s", asset.Ip)
	// }

	// 3. 检查子域名
	subdomainClues := utils.StructFilter(clueList, map[string]interface{}{
		"Type":      clues.TYPE_SUBDOMAIN,
		"IsDeleted": clues.NOT_DELETE,
		"Status":    clues.CLUE_PASS_STATUS,
	})
	urlNew := asset.Url
	topDomain = GetTopDomain(urlNew)

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】开始子域名检查 - 过滤后子域名线索数: %d, asset_url: %s, top_domain: %s",
	// 		len(subdomainClues), urlNew, topDomain)
	// 	for i, subdomainClue := range subdomainClues {
	// 		log.Infof("【IP跟踪】子域名线索[%d] - content: %s, is_fake_icp: %d", i, subdomainClue.Content, subdomainClue.IsFakeIcp)
	// 	}
	// }

	for _, subdomainClue := range subdomainClues {
		clueTopDomain := GetTopDomain(subdomainClue.Content)
		// if isTargetIP {
		// 	log.Infof("【IP跟踪】检查子域名匹配 - asset_top_domain: %s, clue_top_domain: %s, clue_content: %s, url_contains: %v",
		// 		topDomain, clueTopDomain, subdomainClue.Content, strings.Contains(urlNew, subdomainClue.Content))
		// }

		if topDomain != "" && (topDomain == clueTopDomain) && strings.Contains(urlNew, subdomainClue.Content) {
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】子域名匹配成功 - ip: %s, 匹配的子域名: %s", asset.Ip, subdomainClue.Content)
			// }

			cl := findClueByContent(clueList, subdomainClue.Content)
			if cl.IsFakeIcp == 1 {
				if !hasLevel(levelArr, AssetsLevelD) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelD,
							Val:   LevelReason{Level: AssetsLevelD, IsFake: true, Reason: "ICP盗用"},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】子域名匹配，判定为D级(ICP盗用) - ip: %s", asset.Ip)
				// }
			} else {
				if !hasLevel(levelArr, AssetsLevelA) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val:   LevelReason{Level: AssetsLevelA, IsFake: false, Reason: "存在域名-" + subdomainClue.Content + " && " + subdomainClue.Content + "在企业线索库"},
						},
					})
				}
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】子域名匹配，判定为A级 - ip: %s, 匹配子域名: %s", asset.Ip, subdomainClue.Content)
				// }
			}

			// 保存到缓存
			ok := redis.SetCache(cacheKey, CacheDuration, levelArr)
			if !ok {
				log.Errorf("资产分类 - 保存缓存失败")
			}
			log.Infof("子域名判断，判定level为A,ip:%s,assets_id:%s", asset.Ip, asset.Id)
			return nil
		}
	}

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】子域名检查完成，未找到匹配的子域名 - ip: %s", asset.Ip)
	// }

	// 4. 检查证书
	certClues := utils.StructFilter(clueList, map[string]interface{}{
		"Type":      clues.TYPE_CERT,
		"IsDeleted": clues.NOT_DELETE,
		"Status":    clues.CLUE_PASS_STATUS,
	})

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】开始证书检查 - 过滤后证书线索数: %d, assets_from: %d, protocol: %s",
	// 		len(certClues), asset.AssetsFrom, asset.Protocol)
	// 	for i, certClue := range certClues {
	// 		log.Infof("【IP跟踪】证书线索[%d] - content: %s", i, certClue.Content)
	// 	}
	// }

	// 获取资产证书信息
	var cert string
	if asset.AssetsFrom == 1 {
		// 第三方导入的资产
		cert = asset.Cert
		// if isTargetIP {
		// 	log.Infof("【IP跟踪】第三方导入资产 - cert: %s, protocol: %s", cert, asset.Protocol)
		// }

		if cert == "" && asset.Protocol == "https" {
			// 爬取证书信息
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】开始验证证书有效性 - url: %s, port: %s", url, asset.Port)
			// }

			if !certIsValid(url, cast.ToInt(asset.Port), asset.Protocol) {
				// 证书是假的
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】证书无效，进入checkIsFake - ip: %s", asset.Ip)
				// }
				checkIsFake(userID, asset, clueList, levelArr, true, true)
				return nil
			} else {
				// 真的证书
				cert = GetCert(url, cast.ToInt(asset.Port), asset.Protocol)
				// if isTargetIP {
				// 	log.Infof("【IP跟踪】证书有效，获取到证书信息 - cert: %s", cert)
				// }
			}
		} else {
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】无证书或非HTTPS，进入checkIsFake - ip: %s", asset.Ip)
			// }
			checkIsFake(userID, asset, clueList, levelArr, false, false)
			return nil
		}
	} else {
		// fofa的数据
		cert = asset.Cert
		//

		if cert == "" {
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】FOFA数据无证书，进入checkIsFake - ip: %s", asset.Ip)
			// }
			checkIsFake(userID, asset, clueList, levelArr, false, false)
			return nil
		} else if !asset.CertsValid {
			// 假的证书
			// if isTargetIP {
			// 	log.Infof("【IP跟踪】FOFA数据证书无效，进入checkIsFake - ip: %s", asset.Ip)
			// }
			checkIsFake(userID, asset, clueList, levelArr, true, true)
			return nil
		}
	}

	// 处理证书信息
	cnArray, oArray := processCertClues(certClues)
	thisCertCn, thisCertO := parseCert(cert)

	// 检查证书是否匹配
	if contains(cnArray, thisCertCn) || contains(oArray, thisCertO) {
		// B级资产 - 尝试多种格式匹配证书线索
		cl := findCertClue(clueList, thisCertCn, thisCertO)
		if cl == nil {
			log.Warnf("证书判断，未找到线索，thisCertCn: %s, thisCertO: %s", thisCertCn, thisCertO)
			cl = &clues.Clue{}
		}
		isFakeIcp := cl.IsFakeIcp == 1
		if isFakeIcp {
			if !hasLevel(levelArr, AssetsLevelD) {
				levelArr = append(levelArr, LevelItem{
					IP: LevelValue{
						Level: AssetsLevelD,
						Val:   LevelReason{AssetsLevelD, true, "ICP盗用", 0},
					},
				})
			}
		} else {
			if contains(cnArray, thisCertCn) {
				if !hasLevel(levelArr, AssetsLevelA) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val:   LevelReason{AssetsLevelA, false, "存在证书-CN=" + thisCertCn + " && 证书为真 && " + GetTopDomain(thisCertCn) + "在企业线索库", 0},
						},
					})
				}
			} else {
				if !hasLevel(levelArr, AssetsLevelB) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelB,
							Val:   LevelReason{AssetsLevelB, false, "存在证书，但是证书的CN值为空或者CN值不在在企业线索库", 0},
						},
					})
				}
			}
		}
		if !redis.SetCache(cacheKey, CacheDuration, levelArr) {
			log.Errorf("证书判断，保存缓存失败")
		}
		log.Infof("证书判断，判定level为B-line1, ip:%s, assets_id:%s, isFakeIcp:%v", asset.Ip, asset.Id, isFakeIcp)
		return nil
	}

	// 处理通配符证书
	if !contains(cnArray, thisCertCn) {
		if strings.Contains(thisCertCn, "*.") {
			// 通配符证书
			thisCertCn = strings.Replace(thisCertCn, "*.", "", 1)
			for _, cn := range cnArray {
				if strings.HasSuffix(cn, "."+thisCertCn) || cn == thisCertCn {
					// 匹配上证书了，B级资产
					cl := findCertClue(clueList, thisCertCn, thisCertO)
					if cl == nil {
						log.Warnf("证书判断，未找到线索，thisCertCn: %s, thisCertO: %s", thisCertCn, thisCertO)
						cl = &clues.Clue{}
					}
					isFakeIcp := cl.IsFakeIcp == 1

					if isFakeIcp {
						if !hasLevel(levelArr, AssetsLevelD) {
							levelArr = append(levelArr, LevelItem{
								IP: LevelValue{
									Level: AssetsLevelD,
									Val:   LevelReason{AssetsLevelD, true, "ICP盗用", 0},
								},
							})
						}
					} else {
						if contains(cnArray, thisCertCn) {
							if !hasLevel(levelArr, AssetsLevelA) {
								levelArr = append(levelArr, LevelItem{
									IP: LevelValue{
										Level: AssetsLevelA,
										Val:   LevelReason{AssetsLevelA, false, "存在证书-CN=" + thisCertCn + " && 证书为真 && " + GetTopDomain(thisCertCn) + "在企业线索库", 0},
									},
								})
							}
						} else {
							if !hasLevel(levelArr, AssetsLevelB) {
								levelArr = append(levelArr, LevelItem{
									IP: LevelValue{
										Level: AssetsLevelB,
										Val:   LevelReason{AssetsLevelB, false, "存在证书，但是证书的CN值为空或者CN值不在在企业线索库", 0},
									},
								})
							}
						}
					}

					log.Infof("证书判断，判定level为B-line2, ip:%s, assets_id:%s", asset.Ip, asset.Id)
					if !redis.SetCache(cacheKey, CacheDuration, levelArr) {
						log.Errorf("证书判断，保存缓存失败")
					}
					return nil
				}
			}

			// 未匹配上，C级资产
			cl := findCertClue(clueList, thisCertCn, thisCertO)
			if cl == nil {
				log.Warnf("证书判断，未找到线索，thisCertCn: %s, thisCertO: %s", thisCertCn, thisCertO)
				cl = &clues.Clue{}
			}
			isFakeIcp := cl.IsFakeIcp == 1
			if isFakeIcp {
				if !hasLevel(levelArr, AssetsLevelD) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelD,
							Val:   LevelReason{AssetsLevelD, true, "ICP盗用", 0},
						},
					})
				}
			} else {
				if res := MatchThreatenKeyword(asset); res != nil {
					log.Infof("命中威胁词库了, macth-3, reccomment_result_assets_id:%s", asset.Id)
					levelArr = append(levelArr, *res)
				} else {
					if !hasLevel(levelArr, AssetsLevelC) {
						levelArr = append(levelArr, LevelItem{
							IP: LevelValue{
								Level: AssetsLevelC,
								Val:   LevelReason{AssetsLevelC, false, "无正向域名或证书等特征 && 标题与内置黑库不匹配", 0},
							},
						})
					}
				}
				if !redis.SetCache(cacheKey, CacheDuration, levelArr) {
					log.Errorf("证书判断，保存缓存失败")
				}
			}
			return nil
		} else {
			// 非通配符证书
			for _, cn := range cnArray {
				thisLopCertCn := strings.Replace(cn, "*.", "", 1)
				if strings.HasSuffix(thisCertCn, "."+thisLopCertCn) || thisCertCn == thisLopCertCn {
					// 匹配上证书了，B级资产
					cl := findCertClue(clueList, thisLopCertCn, thisCertO)
					if cl == nil {
						cl = &clues.Clue{}
					}
					isFakeIcp := cl.IsFakeIcp == 1

					if isFakeIcp {
						if !hasLevel(levelArr, AssetsLevelD) {
							levelArr = append(levelArr, LevelItem{
								IP: LevelValue{
									Level: AssetsLevelD,
									Val:   LevelReason{AssetsLevelD, true, "ICP盗用", 0},
								},
							})
						}
					} else {
						if contains(cnArray, thisCertCn) {
							if !hasLevel(levelArr, AssetsLevelA) {
								levelArr = append(levelArr, LevelItem{
									IP: LevelValue{
										Level: AssetsLevelA,
										Val:   LevelReason{AssetsLevelA, false, "存在证书-CN=" + thisCertCn + " && 证书为真 && " + GetTopDomain(thisCertCn) + "在企业线索库", 0},
									},
								})
							}
						} else {
							if !hasLevel(levelArr, AssetsLevelB) {
								levelArr = append(levelArr, LevelItem{
									IP: LevelValue{
										Level: AssetsLevelB,
										Val:   LevelReason{AssetsLevelB, false, "存在证书，但是证书的CN值为空或者CN值不在在企业线索库", 0},
									},
								})
							}
						}
					}

					log.Infof("证书判断，判定level为B-line3, ip:%s, assets_id:%s", asset.Ip, asset.Id)
					if !redis.SetCache(cacheKey, CacheDuration, levelArr) {
						log.Errorf("证书判断，保存缓存失败")
					}
					return nil
				}
			}

			// 未匹配上，C级资产
			cl := findCertClue(clueList, thisCertCn, thisCertO)
			if cl == nil {
				log.Warnf("证书判断，未找到线索，thisCertCn: %s, thisCertO: %s", thisCertCn, thisCertO)
				cl = &clues.Clue{}
			}
			isFakeIcp := cl.IsFakeIcp == 1
			if isFakeIcp {
				if !hasLevel(levelArr, AssetsLevelD) {
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelD,
							Val:   LevelReason{AssetsLevelD, true, "ICP盗用", 0},
						},
					})
				}
			} else {
				if res := MatchThreatenKeyword(asset); res != nil {
					log.Infof("命中威胁词库了, macth-3, reccomment_result_assets_id:%s", asset.Id)
					levelArr = append(levelArr, *res)
				} else {
					if !hasLevel(levelArr, AssetsLevelC) {
						levelArr = append(levelArr, LevelItem{
							IP: LevelValue{
								Level: AssetsLevelC,
								Val:   LevelReason{AssetsLevelC, false, "无正向域名或证书等特征 && 标题与内置黑库不匹配", 0},
							},
						})
					}
				}
				if !redis.SetCache(cacheKey, CacheDuration, levelArr) {
					log.Errorf("证书判断，保存缓存失败")
				}
			}
			return nil
		}
	}

	// 默认返回
	// if isTargetIP {
	// 	log.Infof("【IP跟踪】ClassifyAssetsLevel处理完成，但没有匹配到任何条件 - ip: %s, 进入checkIsFake流程", asset.Ip)
	// }
	return nil
}

func checkIsFake(userID uint64, asset *recommend_result.RecommendResult, cl []*clues.Clue, levelArr []LevelItem, hasCert, isFakeCert bool) []LevelItem {
	// targetIPs := []string{"*************", "*************"}
	// isTargetIP := false
	// for _, targetIP := range targetIPs {
	// 	if asset.Ip == targetIP {
	// 		isTargetIP = true
	// 		break
	// 	}
	// }
	// if isTargetIP {
	// 	log.Infof("【IP跟踪】进入checkIsFake - ip: %s, has_cert: %v, is_fake_cert: %v, protocol: %s",
	// 		asset.Ip, hasCert, isFakeCert, asset.Protocol)
	// }

	isFake := false
	isA := false
	var fakeType int
	fakeReason := ""
	protocol := asset.Protocol

	var cacheBlackTitle []string
	// 从缓存获取黑名单标题
	ok := redis.GetCache("black_title_keyword", &cacheBlackTitle)
	if !ok {
		// 从数据库获取所有活跃的关键词
		keywords, err := black_keyword_system.NewModel().ListAllActiveKeyword()
		if err != nil {
			log.Errorf("checkIsFake - ListAllActiveKeyword err: %v", err)
			return nil
		}
		redis.SetCache("black_title_keyword", CacheDuration, keywords)
		cacheBlackTitle = keywords
	}

	// 去重
	blackTitleList := uniqueStrings(cacheBlackTitle)

	if protocol == "https" {
		// 判断icon是否相同
		hash := asset.Logo.Hash
		if hash != 0 {
			clueNew := utils.StructFilter(cl, map[string]interface{}{
				"Type":      clues.TYPE_LOGO,
				"IsDeleted": clues.NOT_DELETE,
				"Status":    clues.CLUE_PASS_STATUS,
			})
			for _, value := range clueNew {
				if value.Hash == hash {
					isFake = true
					fakeType = THREATEN_TYPE_DY
					fakeReason = "ICON盗用"
					break
				}
			}
		}

		// 判断关键词是否相同
		if !isFake {
			title := asset.Title
			if title != "" {
				clueNew := utils.StructFilter(cl, map[string]interface{}{
					"Type":      clues.TYPE_KEYWORD,
					"IsDeleted": clues.NOT_DELETE,
					"Status":    clues.CLUE_PASS_STATUS,
				})
				for _, value := range clueNew {
					if value.Content == title {
						isFake = true
						fakeType = THREATEN_TYPE_OTHER
						fakeReason = "关键词盗用"
						break
					}
				}
			}
		}

		// 判断icp是否相同
		if !isFake {
			icp := asset.Icp
			if icp != "" {
				icpParts := strings.Split(icp, "-")
				icpString := ""
				if len(icpParts) > 0 {
					icpString = icpParts[0]
				}

				if icpString != "" {
					clueNew := utils.StructFilter(cl, map[string]interface{}{
						"Type":      clues.TYPE_ICP,
						"IsDeleted": clues.NOT_DELETE,
						"Status":    clues.CLUE_PASS_STATUS,
					})
					for _, value := range clueNew {
						valueParts := strings.Split(value.Content, "-")
						valueString := value.Content
						if len(valueParts) > 0 {
							valueString = valueParts[0]
						}

						if valueString == icpString {
							// 检查IP是否在同一个B段
							ipParts := strings.Split(asset.Ip, ".")
							var firstTwoParts []string
							if len(ipParts) >= 2 {
								firstTwoParts = ipParts[:2]
							}
							searchKeyword := strings.Join(firstTwoParts, ".") + "."
							condition := fofaee_assets.NewFindCondition()
							condition.Ip = searchKeyword
							condition.UserId = userID
							condition.Status = []int{fofaee_assets.StatusConfirmAsset, fofaee_assets.StatusUploadAsset}
							_, total, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(context.Background(), condition, 1, 1)
							if err != nil {
								log.Errorf("checkIsFake - FindByCondition err: %v", err)
								continue
							}
							if total == 0 {
								isFake = true
								fakeType = THREATEN_TYPE_FM_ICP
								fakeReason = "ICP仿冒"
								break
							} else {
								isA = true
								log.Infof("checkIsFake-当前资产没被判定为icp盗用，因为存在于该ip在同一个b段的ip - asset_id: %s", asset.Id)
								break
							}
						}
					}
				}
			}
		}

		// 判断域名相似度80%
		if !isFake {
			url := asset.Url
			if url != "" {
				urlParts := strings.Split(url, "://")
				urlDomain := ""
				if len(urlParts) > 1 {
					urlDomain = urlParts[1]
				}

				if urlDomain != "" {
					urlDomain = strings.ReplaceAll(urlDomain, ".", "")
					if urlDomain != "" {
						clueNew := utils.StructFilter(cl, map[string]interface{}{
							"Type":      clues.TYPE_DOMAIN,
							"IsDeleted": clues.NOT_DELETE,
							"Status":    clues.CLUE_PASS_STATUS,
						})
						for _, value := range clueNew {
							v := strings.ReplaceAll(value.Content, ".", "")
							if strings.HasPrefix(strings.ToLower(urlDomain), strings.ToLower(v)) && urlDomain != asset.Title {
								isFake = true
								fakeReason = "域名混淆"
								fakeType = THREATEN_TYPE_DOMAIN_CONFUSION
								break
							}

							_, percent := SimilarText(urlDomain, v)
							if percent > 80 { // 假设配置的域相似度阈值为80
								isFake = true
								fakeReason = "域名混淆"
								fakeType = THREATEN_TYPE_DOMAIN_CONFUSION
								break
							}
						}
					}
				}
			}
		}

		// 判断关键词是否在黑名单中
		if !isFake {
			title := asset.Title
			if title != "" {
				for _, value := range blackTitleList {
					if strings.Contains(title, value) || title == value {
						log.Infof("classifyAssetsLevel - 仿冒资产,ip: %s, title: %s", asset.Ip, title)
						isFake = true
						fakeReason = "无正向域名或证书等特征 && 标题与内置黑库匹配"
						// 从数据库查询fakeType
						black, err := black_keyword_system.NewModel().First(mysql.WithWhere("keyword = ?", value))
						if err != nil {
							log.Errorf("checkIsFake - GetFakeType err: %v", err)
							break
						}
						if black.TypeId == 0 {
							fakeType = THREATEN_TYPE_HDD
						} else {
							fakeType = int(black.TypeId)
						}
						break
					}
				}
			}
		}
	} else {
		title := asset.Title
		if title != "" {
			for _, value := range blackTitleList {
				if strings.Contains(title, value) || title == value {
					isFake = true
					fakeReason = "无正向域名或证书等特征 && 标题与内置黑库匹配"
					// 从数据库查询fakeType
					black, err := black_keyword_system.NewModel().First(mysql.WithWhere("keyword = ?", value))
					if err != nil {
						log.Errorf("checkIsFake - GetFakeType err: %v", err)
						break
					}
					if black.TypeId == 0 {
						fakeType = THREATEN_TYPE_HDD
					} else {
						fakeType = int(black.TypeId)
					}
					break
				}
			}
		}
		// 判断icp是否相同
		if !isFake {
			icp := asset.Icp
			if icp != "" {
				icpParts := strings.Split(icp, "-")
				icpString := ""
				if len(icpParts) > 0 {
					icpString = icpParts[0]
				}

				if icpString != "" {
					clueNew := utils.StructFilter(cl, map[string]interface{}{
						"Type":      clues.TYPE_ICP,
						"IsDeleted": clues.NOT_DELETE,
						"Status":    clues.CLUE_PASS_STATUS,
					})
					for _, value := range clueNew {
						valueParts := strings.Split(value.Content, "-")
						valueString := value.Content
						if len(valueParts) > 0 {
							valueString = valueParts[0]
						}

						if valueString == icpString {
							// 检查IP是否在同一个B段
							ipParts := strings.Split(asset.Ip, ".")
							var firstTwoParts []string
							if len(ipParts) >= 2 {
								firstTwoParts = ipParts[:2]
							}
							searchKeyword := strings.Join(firstTwoParts, ".") + "."
							condition := fofaee_assets.NewFindCondition()
							condition.Ip = searchKeyword
							condition.UserId = userID
							condition.Status = []int{fofaee_assets.StatusConfirmAsset, fofaee_assets.StatusUploadAsset}
							_, total, err := fofaee_assets.NewFofaeeAssetsModel().FindByCondition(context.Background(), condition, 1, 1)
							if err != nil {
								log.Errorf("checkIsFake - FindByCondition err: %v", err)
								continue
							}
							if total == 0 {
								isFake = true
								fakeType = THREATEN_TYPE_FM_ICP
								fakeReason = "ICP仿冒"
								break
							} else {
								isA = true
								log.Infof("checkIsFake-当前资产没被判定为icp盗用，因为存在于该ip在同一个b段的ip - asset_id: %s", asset.Id)
								break
							}
						}
					}
				}
			}
		}

		// 判断域名前缀
		if !isFake {
			url := asset.Url
			if url != "" {
				urlParts := strings.Split(url, "://")
				urlDomain := ""
				if len(urlParts) > 1 {
					urlDomain = urlParts[1]
				}
				if urlDomain != "" {
					urlDomain = strings.ReplaceAll(urlDomain, ".", "")
					if urlDomain != "" {
						clueNew := utils.StructFilter(cl, map[string]interface{}{
							"Type":      clues.TYPE_DOMAIN,
							"IsDeleted": clues.NOT_DELETE,
							"Status":    clues.CLUE_PASS_STATUS,
						})
						for _, value := range clueNew {
							v := strings.ReplaceAll(value.Content, ".", "")
							if strings.HasPrefix(strings.ToLower(urlDomain), strings.ToLower(v)) && urlDomain != asset.Title {
								isFake = true
								fakeReason = "ICP仿冒"
								fakeType = THREATEN_TYPE_DY
								break
							}
						}
					}
				}
			}
		}
	}

	if levelArr != nil {
		// 检查是否已有D级别
		hasLevelD := false
		for _, l := range levelArr {
			if l.IP.Level == AssetsLevelD {
				hasLevelD = true
				break
			}
		}

		if !hasLevelD {
			if isFake {
				// 是仿冒
				levelArr = append(levelArr, LevelItem{
					IP: LevelValue{
						Level: AssetsLevelD,
						Val: LevelReason{
							Level:    AssetsLevelD,
							IsFake:   isFake,
							FakeType: fakeType,
							Reason:   fakeReason,
						},
					},
				})
				log.Info("classifyAssetsLevel-1 - 判定level为D, ip: " + asset.Ip)
			} else {
				if isA {
					fakeReason = "无明显正向特征,台账存在相同B段内的ip，并且当前icp存在于企业线索库,icp为：" + asset.Icp
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val: LevelReason{
								Level:    AssetsLevelA,
								IsFake:   false,
								FakeType: 0,
								Reason:   fakeReason,
							},
						},
					})
					log.Info("classifyAssetsLevel-2 - 判定level为A, ip: " + asset.Ip)
				} else {
					// 不是仿冒
					fakeReason = "无明显正向特征"
					levelArr = append(levelArr, LevelItem{
						IP: LevelValue{
							Level: AssetsLevelC,
							Val: LevelReason{
								Level:    AssetsLevelC,
								IsFake:   false,
								FakeType: 0,
								Reason:   fakeReason,
							},
						},
					})
					log.Infof("classifyAssetsLevel - 当前资产没有明显的正向或者反向特征，标记为C %+v", map[string]interface{}{"asset_id": asset.Id, "asset_ip": asset.Ip, "user_id": userID})
				}
			}
		} else {
			// 已经存在levelD的时候。判断存在的是不是仿冒资产
			if isFake {
				// 查询已经存在的cache里面的是不是仿冒的
				for i, l := range levelArr {
					if l.IP.Level == AssetsLevelD {
						if l.IP.Val.IsFake {
							// 如果是假的
							log.Info("classifyAssetsLevel-3", "判定level为D,ip:"+asset.Ip)
							levelArr[i].IP.Val = LevelReason{AssetsLevelD, true, fakeReason, fakeType}
						}
					}
				}
			}
		}
	} else {
		// 如果是仿冒，那么标记为D，如果不是仿冒标记为C
		if isFake {
			levelArr = []LevelItem{
				{
					IP: LevelValue{
						Level: AssetsLevelD,
						Val:   LevelReason{AssetsLevelD, isFake, fakeReason, fakeType},
					},
				},
			}
			log.Info("classifyAssetsLevel-4 - 判定level为D, ip: " + asset.Ip)
		} else {
			// 如果是 判断高可信度里是否有相同网段的ip在 - 如果有的话不将此资产判断为威胁资产，判断为高可信度
			if isA {
				reason := "无正向域名或证书,台账存在相同B段内的ip，并且当前icp存在于企业线索库,icp为：" + asset.Icp
				levelArr = []LevelItem{
					{
						IP: LevelValue{
							Level: AssetsLevelA,
							Val:   LevelReason{AssetsLevelA, false, reason, 0},
						},
					},
				}
				log.Info("classifyAssetsLevel-5 - 判定level为A, ip: " + asset.Ip)
			} else {
				reason := "无明显正向特征"
				if hasCert {
					if isFakeCert {
						reason = "存在证书  && 证书为假"
						fakeType = THREATEN_TYPE_OTHER
					} else {
						reason = "无正向域名或证书等特征 && 标题与内置黑库不匹配"
					}
				}
				// 再加一种判断，该资产没有域名，没有证书，但是有icp，然后标题里面又包含关键词
				if asset.Domain == "" && !hasCert && asset.Icp != "" {
					blackTitle2 := []string{"管理", "系统", "平台", "Login", "CRM", "OA", "Manager", "Login", "Admin", "登录"}
					title := asset.Title
					if title != "" {
						for _, value := range blackTitle2 {
							if strings.Contains(title, value) || title == value {
								isFake = false
								reason = "无正向域名或证书等特征 && 标题与关键词列表匹配"
								break
							}
						}
					}
				}
				log.Info("classifyAssetsLevel-7 - 判定level为C, ip: " + asset.Ip)
				levelArr = []LevelItem{
					{
						IP: LevelValue{
							Level: AssetsLevelC,
							Val:   LevelReason{AssetsLevelC, false, reason, 0},
						},
					},
				}

			}
		}
	}

	// 缓存结果
	cacheKey := cache.GetCacheKey("classifyAssetsLevel", fmt.Sprintf("%d_%s_level", userID, asset.Ip))
	redis.SetCache(cacheKey, time.Hour*24, levelArr)

	// if isTargetIP {
	// 	log.Infof("【IP跟踪】checkIsFake完成 - ip: %s, is_fake: %v, is_a: %v, fake_type: %d, reason: %s, 最终级别数: %d",
	// 		asset.Ip, isFake, isA, fakeType, fakeReason, len(levelArr))
	// 	for i, level := range levelArr {
	// 		log.Infof("【IP跟踪】最终级别[%d] - level: %d, is_fake: %v, reason: %s",
	// 			i, level.IP.Level, level.IP.Val.IsFake, level.IP.Val.Reason)
	// 	}
	// }

	return levelArr
}

// 辅助函数

func findClueByContent(cl []*clues.Clue, content string) *clues.Clue {
	for _, clue := range cl {
		if clue.Content == content {
			return clue
		}
	}
	return nil
}

// findCertClue 尝试多种格式匹配证书线索
func findCertClue(clueList []*clues.Clue, thisCertCn, thisCertO string) *clues.Clue {
	// 尝试匹配CN
	if thisCertCn != "" {
		// 1. 直接匹配原始CN
		if cl := findClueByContent(clueList, thisCertCn); cl != nil {
			return cl
		}

		// 2. 尝试添加CN=前缀匹配
		cnWithPrefix := fmt.Sprintf("CN=\"%s\"", thisCertCn)
		if cl := findClueByContent(clueList, cnWithPrefix); cl != nil {
			return cl
		}

		// 3. 如果CN包含通配符，尝试去掉通配符匹配
		if strings.HasPrefix(thisCertCn, "*.") {
			cnWithoutWildcard := strings.Replace(thisCertCn, "*.", "", 1)
			if cl := findClueByContent(clueList, cnWithoutWildcard); cl != nil {
				return cl
			}
			// 4. 去掉通配符后再加CN=前缀
			cnWithoutWildcardWithPrefix := fmt.Sprintf("CN=\"%s\"", cnWithoutWildcard)
			if cl := findClueByContent(clueList, cnWithoutWildcardWithPrefix); cl != nil {
				return cl
			}
		}
	}

	// 尝试匹配O
	if thisCertO != "" {
		// 1. 直接匹配原始O
		if cl := findClueByContent(clueList, thisCertO); cl != nil {
			return cl
		}

		// 2. 尝试添加O=前缀匹配
		oWithPrefix := fmt.Sprintf("O=\"%s\"", thisCertO)
		if cl := findClueByContent(clueList, oWithPrefix); cl != nil {
			return cl
		}
	}

	return nil
}

func hasLevel(levelArr []LevelItem, level int) bool {
	for _, item := range levelArr {
		if item.IP.Level == level {
			return true
		}
	}
	return false
}

func isIPInRange(ipStr, ipRangeStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 处理IP段格式 (如: ***********/24)
	if strings.Contains(ipRangeStr, "/") {
		_, ipNet, err := net.ParseCIDR(ipRangeStr)
		if err != nil {
			return false
		}
		return ipNet.Contains(ip)
	}

	// 处理单个IP
	if ipStr == ipRangeStr {
		return true
	}

	// 处理IP范围 (如: ***********-***********00)
	if strings.Contains(ipRangeStr, "-") {
		parts := strings.Split(ipRangeStr, "-")
		if len(parts) != 2 {
			return false
		}

		startIP := net.ParseIP(parts[0])
		endIP := net.ParseIP(parts[1])
		if startIP == nil || endIP == nil {
			return false
		}

		return bytesCompare(ip, startIP) >= 0 && bytesCompare(ip, endIP) <= 0
	}

	return false
}

func bytesCompare(a, b net.IP) int {
	a = a.To16()
	b = b.To16()
	for i := 0; i < len(a); i++ {
		switch {
		case a[i] > b[i]:
			return 1
		case a[i] < b[i]:
			return -1
		}
	}
	return 0
}

// CertIsValid 检查指定域名的SSL证书是否有效
func certIsValid(domain string, port int, protocol string) bool {
	// 生成缓存key
	cacheKey := fmt.Sprintf("valid_cert:%s:%s:%d", protocol, domain, port)
	// 尝试从缓存中获取结果
	var cached bool
	if found := redis.GetCache(cacheKey, &cached); found {
		return cached
	}
	// 如果协议不是HTTPS，直接返回false
	if protocol != "https" {
		redis.SetCache(cacheKey, CacheDuration, false)
		return false
	}
	// 处理域名，移除https://前缀
	domain = strings.TrimPrefix(domain, "https://")
	// 检查SSL证书有效性
	isValid := checkSSLCertificate(domain, port)
	// 缓存结果
	redis.SetCache(cacheKey, CacheDuration, isValid)
	return isValid
}

// checkSSLCertificate 实际检查SSL证书的函数
func checkSSLCertificate(domain string, port int) bool {
	// 设置超时时间
	dialer := &net.Dialer{
		Timeout: 10 * time.Second,
	}
	// 建立TLS连接
	conn, err := tls.DialWithDialer(dialer, "tcp", fmt.Sprintf("%s:%d", domain, port), &tls.Config{
		InsecureSkipVerify: true, // 跳过证书有效性验证，手动验证
	})
	if err != nil {
		return false
	}
	defer func(conn *tls.Conn) {
		_ = conn.Close()
	}(conn)
	// 获取证书信息
	state := conn.ConnectionState()
	if len(state.PeerCertificates) == 0 {
		return false
	}
	// 检查证书是否有效
	cert := state.PeerCertificates[0]
	now := time.Now()
	if now.Before(cert.NotBefore) || now.After(cert.NotAfter) {
		return false
	}
	return true
}

func processCertClues(clues []*clues.Clue) ([]string, []string) {
	var cnArray, oArray []string
	for _, clue := range clues {
		value := strings.Replace(clue.Content, "cn=", "CN=", -1)
		value = strings.Replace(value, "o=", "O=", -1)
		value = strings.Replace(value, "Cn=", "CN=", -1)
		value = strings.Replace(value, "cN=", "CN=", -1)

		center := strings.Split(value, "CN=\"")
		if len(center) > 1 {
			parts := strings.Split(center[1], "O=\"")
			if len(parts) > 1 {
				cn := strings.Trim(strings.Replace(parts[0], "\"", "", -1), " ")
				o := strings.Trim(strings.Replace(parts[1], "\"", "", -1), " ")
				if cn != "" {
					cnArray = append(cnArray, cn)
				}
				if o != "" {
					oArray = append(oArray, o)
				}
			} else {
				// 只有CN
				certDomain := strings.Trim(strings.Replace(center[1], "\"", "", -1), " ")
				if certDomain != "" {
					cnArray = append(cnArray, certDomain)
					cnArray = append(cnArray, strings.Replace(certDomain, "*.", "", 1))
				}
			}
		} else {
			// 只有O
			parts := strings.Split(value, "O=\"")
			if len(parts) > 1 {
				o := strings.Trim(strings.Replace(parts[1], "\"", "", -1), " ")
				if o != "" {
					oArray = append(oArray, o)
				}
			}
		}
	}

	// 去重
	cnArray = uniqueStrings(cnArray)
	oArray = uniqueStrings(oArray)

	return cnArray, oArray
}

func parseCert(cert string) (string, string) {
	cert = strings.Replace(cert, "cn=", "CN=", -1)
	cert = strings.Replace(cert, "o=", "O=", -1)
	cert = strings.Replace(cert, "Cn=", "CN=", -1)
	cert = strings.Replace(cert, "cN=", "CN=", -1)

	var thisCertO, thisCertCn string

	center := strings.Split(cert, "CN=\"")
	if len(center) > 1 {
		parts := strings.Split(center[1], "O=\"")
		if len(parts) > 1 {
			// 有CN和O两个字段
			cnPart := strings.Trim(strings.Replace(parts[0], "\"", "", -1), " ,")
			thisCertCn = cnPart
			thisCertO = strings.Trim(strings.Replace(parts[1], "\"", "", -1), " ")
		} else {
			// 只有CN，可能包含逗号分隔的其他字段
			cnPart := center[1]
			// 处理可能的逗号分隔
			if strings.Contains(cnPart, ",") {
				cnPart = strings.Split(cnPart, ",")[0]
			}
			thisCertCn = strings.Trim(strings.Replace(cnPart, "\"", "", -1), " ")
		}
	} else {
		// 只有O
		parts := strings.Split(cert, "O=\"")
		if len(parts) > 1 {
			thisCertO = strings.Trim(strings.Replace(parts[1], "\"", "", -1), " ")
		}
	}

	return thisCertCn, thisCertO
}

func contains(arr []string, str string) bool {
	for _, s := range arr {
		if s == str {
			return true
		}
	}
	return false
}

func uniqueStrings(arr []string) []string {
	keys := make(map[string]bool)
	var list []string
	for _, entry := range arr {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

// GetTopDomain 获取顶级域名
func GetTopDomain(domain string) string {
	if domain == "" {
		log.Warn("GetTopDomain: empty domain provided")
		return ""
	}
	if !strings.Contains(domain, "://") {
		domain = "https://" + domain
	}
	// 处理带有 "--" 的特殊域名（如 Punycode 域名）
	if strings.Count(domain, "--") > 0 {
		domain = strings.ReplaceAll(domain, "-", "")
	}
	// 使用 go-tld 解析域名
	parsed, err := tld.Parse(domain)
	if err != nil {
		log.Errorf("GetTopDomain: parsing error: %v, url: %s\n", err, domain)
		return ""
	}
	// 获取注册域名
	topDomain := parsed.Domain + "." + parsed.TLD
	// Punycode 解码
	decoded, err := idna.ToUnicode(topDomain)
	if err != nil {
		log.Errorf("GetTopDomain: Punycode decode error: %v\n", err)
		return topDomain // 返回原始值
	}
	return decoded
}

// SimilarText 计算两个字符串的相似度，类似php的similar_text函数
func SimilarText(first, second string) (int, float64) {
	var sum, m, p, q, r int
	pos1, pos2 := 0, 0
	m = 0
	// 找出两个字符串中最长的相同子串的长度
	for p = 0; p < len(first); p++ {
		for q = 0; q < len(second); q++ {
			for r = 0; (p+r) < len(first) && (q+r) < len(second) && first[p+r] == second[q+r]; r++ {
				// 空循环体，只增加r
			}
			if r > m {
				m = r
				pos1 = p
				pos2 = q
			}
		}
	}
	sum = m
	// 递归计算剩余部分的相似度
	if sum > 0 {
		if pos1 > 0 && pos2 > 0 {
			subSum, _ := SimilarText(first[0:pos1], second[0:pos2])
			sum += subSum
		}
		if (pos1+m) < len(first) && (pos2+m) < len(second) {
			subSum, _ := SimilarText(first[pos1+m:], second[pos2+m:])
			sum += subSum
		}
	}
	// 计算相似度百分比
	percent := float64(sum*200) / float64(len(first)+len(second))
	return sum, percent
}

// GetCert 获取证书信息
func GetCert(domain string, port int, protocol string) string {
	cacheKey := fmt.Sprintf("cert:%s:%s:%d", protocol, domain, port)
	var val string
	if !redis.GetCache(cacheKey, &val) {
		if protocol != "https" {
			return ""
		}
		// 清理域名
		domain = strings.Replace(domain, "https://", "", 1)
		// 建立TLS连接获取证书
		conn, err := tls.Dial("tcp", fmt.Sprintf("%s:%d", domain, port), &tls.Config{
			InsecureSkipVerify: true,
		})
		if err != nil {
			return ""
		}
		defer func(conn *tls.Conn) {
			_ = conn.Close()
		}(conn)
		// 获取证书链
		certs := conn.ConnectionState().PeerCertificates
		if len(certs) == 0 {
			return ""
		}
		cert := certs[0]
		// 定义OID常量
		var (
			oidOrganization = []int{2, 5, 4, 10} // OID for organization (O)
			oidCommonName   = []int{2, 5, 4, 3}  // OID for common name (CN)
		)
		// 解析证书主题
		var subject []string
		for _, name := range cert.Subject.Names {
			switch {
			case name.Type.Equal(oidOrganization):
				if org, ok := name.Value.(string); ok && org != "" {
					subject = append(subject, fmt.Sprintf("O=\"%s\"", org))
				}
			case name.Type.Equal(oidCommonName):
				if cn, ok := name.Value.(string); ok && cn != "" {
					subject = append(subject, fmt.Sprintf("CN=\"%s\"", cn))
				}
			}
		}
		if len(subject) > 0 {
			result := strings.Join(subject, " ")
			return result
		}
		return ""
	}
	if val == "" {
		return ""
	}
	return val
}

// MatchThreatenKeyword 检查资产标题是否匹配威胁关键词
func MatchThreatenKeyword(asset *recommend_result.RecommendResult) *LevelItem {
	isFake := false
	var fakeType int
	fakeReason := ""
	var levelItem LevelItem
	var cacheBlackTitle []string
	// 从缓存获取黑名单标题
	ok := redis.GetCache("black_title_keyword", &cacheBlackTitle)
	if !ok {
		// 从数据库获取所有活跃的关键词
		keywords, err := black_keyword_system.NewModel().ListAllActiveKeyword()
		if err != nil {
			log.Errorf("checkIsFake - ListAllActiveKeyword err: %v", err)
			return nil
		}
		redis.SetCache("black_title_keyword", CacheDuration, keywords)
		cacheBlackTitle = keywords
	}

	// 去重
	blackTitleList := uniqueStrings(cacheBlackTitle)
	// 判断关键词是否相同
	if asset.Title != "" {
		for _, value := range blackTitleList {
			if strings.Contains(asset.Title, value) || asset.Title == value {
				isFake = true
				fakeReason = "无正向域名或证书等特征 && 标题与内置黑库匹配"

				// 查询fakeType
				black, err := black_keyword_system.NewModel().First(mysql.WithWhere("keyword = ?", value))
				if err != nil {
					log.Errorf("checkIsFake - GetFakeType err: %v", err)
					break
				}
				if black.TypeId == 0 {
					fakeType = THREATEN_TYPE_HDD
				} else {
					fakeType = int(black.TypeId)
				}
				// 构建LevelItem
				levelItem = LevelItem{
					IP: LevelValue{
						Level: AssetsLevelD,
						Val: LevelReason{
							Level:    AssetsLevelD,
							IsFake:   isFake,
							Reason:   fakeReason,
							FakeType: fakeType,
						},
					},
				}
				break
			}
		}
	}
	return &levelItem
}

// ParseQueryStr 将线索转换为FOFA查询语句
// 对应PHP中的parseQueryStr方法
func ParseQueryStr(params []*clues.Clue, certValid bool, isDomain bool) string {
	result := []string{}
	isSubdomain := false
	var topDomain string

	for _, item := range params {
		prefix, ok := ATTRIBUTE_MAP[item.Type]
		if !ok {
			continue
		}

		switch item.Type {
		case clues.TYPE_CERT:
			// 处理证书类型
			if content := transferCert(item.Content); content != "" {
				result = append(result, content)
			}
		case clues.TYPE_LOGO:
			// 处理logo类型
			result = append(result, fmt.Sprintf("icon_hash=\"%d\"", item.Hash))
		case clues.TYPE_IP:
			// 处理IP类型
			if strings.Contains(item.Content, "-") {
				// 处理IP范围
				// 简化处理，直接使用IP范围查询
				result = append(result, fmt.Sprintf("ip=\"%s\"", item.Content))
			} else {
				result = append(result, fmt.Sprintf("ip=\"%s\"", item.Content))
			}
		case clues.TYPE_ICP:
			// 处理ICP类型
			content := utils.RemoveIcpNumber(item.Content)
			if content != "" {
				orConditions := []string{
					fmt.Sprintf("%s=\"%s\"", prefix, content),
					fmt.Sprintf("body=\"%s\"", content),
				}
				result = append(result, fmt.Sprintf("(%s)", strings.Join(orConditions, " || ")))
			}
		case clues.TYPE_FID:
			// 处理FID类型
			result = append(result, fmt.Sprintf("fid=\"%s\"", item.Content))
		case clues.TYPE_DOMAIN:
			// 处理域名类型
			if strings.Count(item.Content, ".") > 1 {
				isSubdomain = true
				topDomain = utils.GetTopDomain(item.Content)
				result = append(result, fmt.Sprintf("%s=\"%s\"", "host", item.Content))
			} else {
				result = append(result, fmt.Sprintf("%s=\"%s\"", prefix, item.Content))
				if certValid {
					result = append(result, fmt.Sprintf("cert=\"%s\"", item.Content))
				}
			}
		case clues.TYPE_SUBDOMAIN:
			// 处理子域名类型
			result = append(result, fmt.Sprintf("%s=\"%s\"", prefix, item.Content))
		case clues.TYPE_KEYWORD:
			// 处理关键词类型
			if utils.CheckIsChinese(item.Content) {
				// 中文关键词，使用title或body搜索
				orConditions := []string{
					fmt.Sprintf("title=\"%s\"", item.Content),
					fmt.Sprintf("body=\"%s\"", item.Content),
				}
				result = append(result, fmt.Sprintf("(%s)", strings.Join(orConditions, " || ")))
			} else {
				// 非中文关键词，直接使用title搜索
				result = append(result, fmt.Sprintf("%s=\"%s\"", prefix, item.Content))
			}
		default:
			// 其他类型，直接使用前缀和内容
			result = append(result, fmt.Sprintf("%s=\"%s\"", prefix, item.Content))
		}
	}

	// 处理特殊情况
	if isDomain && isSubdomain && topDomain != "" {
		result = append(result, fmt.Sprintf("domain=\"%s\"", topDomain))
	}

	// 组合查询条件
	return strings.Join(result, " && ")
}

// ParseHunterQueryStr 将线索转换为Hunter查询语句
// 对应PHP中的parseHunterQueryStr方法
func ParseHunterQueryStr(params []*clues.Clue) string {
	result := []string{}

	for _, item := range params {
		// 根据不同类型处理查询条件
		switch item.Type {
		case clues.TYPE_CERT:
			// 处理证书类型
			if content := transferCert(item.Content); content != "" {
				result = append(result, content)
			}
		case clues.TYPE_LOGO:
			// 处理logo类型
			result = append(result, fmt.Sprintf("icon_hash=\"%d\"", item.Hash))
		case clues.TYPE_ICP:
			// 处理ICP类型
			content := utils.RemoveIcpNumber(item.Content)
			if content != "" {
				result = append(result, fmt.Sprintf("(cert.subject_org=\"%s\")", content))
			}
		case clues.TYPE_DOMAIN:
			// 处理域名类型
			result = append(result, fmt.Sprintf("domain.suffix=%s", item.Content))
		case clues.TYPE_SUBDOMAIN:
			// 处理子域名类型
			topDomain := utils.GetTopDomain(item.Content)
			if topDomain != "" {
				result = append(result, fmt.Sprintf("(domain=%s && domain.suffix=%s)", item.Content, topDomain))
			}
		}
	}

	// 组合查询条件
	return strings.Join(result, " || ")
}

// transferCert 转换证书内容
// 对应PHP中的transferCert方法
func transferCert(content string) string {
	if content == "" {
		return ""
	}

	// 定义搜索模板
	domainSearchSql := "(cert=\"CommonName: %s\" || cert=\"CommonName: *.%s\")"
	commonSearchSql := "(cert.subject=\"%s\")"

	// 检查是否包含CN=或O=
	if strings.Contains(content, "CN=") || strings.Contains(content, "cn=") ||
		strings.Contains(content, "O=") || strings.Contains(content, "o=") {
		cnoCerts := []string{}

		// 提取CN值
		cnMatch := regexp.MustCompile(`(?i)CN="(.*?)"`).FindStringSubmatch(content)
		if len(cnMatch) > 1 {
			cn := cnMatch[1]
			if utils.IsDomain(cn) {
				// 如果是域名，移除通配符
				cn = strings.Replace(cn, "*.", "", -1)
				cnoCerts = append(cnoCerts, fmt.Sprintf(domainSearchSql, cn, cn))
			} else {
				cnoCerts = append(cnoCerts, fmt.Sprintf(commonSearchSql, cn))
			}
		}

		// 提取O值
		oMatch := regexp.MustCompile(`(?i)O="(.*?)"`).FindStringSubmatch(content)
		if len(oMatch) > 1 {
			o := oMatch[1]
			cnoCerts = append(cnoCerts, fmt.Sprintf(commonSearchSql, o))
		}

		// 根据结果数量返回
		if len(cnoCerts) == 0 {
			return ""
		} else if len(cnoCerts) == 1 {
			return cnoCerts[0]
		} else {
			return "(" + strings.Join(cnoCerts, " || ") + ")"
		}
	}

	// 检查是否是有效域名
	if utils.IsDomain(content) {
		return fmt.Sprintf(domainSearchSql, content, content)
	} else {
		return fmt.Sprintf(commonSearchSql, content)
	}
}

// GetReason 获取推荐原因
// 对应PHP中的getReason函数
func GetReason(reasons []recommend_result.RecommendReason) []string {
	if len(reasons) == 0 {
		return nil
	}

	result := make([]string, 0, len(reasons))
	for _, reason := range reasons {
		clueCompanyName := reason.ClueCompanyName
		if clueCompanyName == "" {
			clueCompanyName = "-"
		}

		typeName := utils.TypeToCn(reason.Type)
		if reason.Type == clues.TYPE_LOGO {
			// Logo类型特殊处理
			imgUrl := utils.GenDownloadUrl(reason.Content, "", false)
			result = append(result, fmt.Sprintf("根据%s的%s <img class=\"reason_ico\" src=\"%s\" />推荐;", clueCompanyName, typeName, imgUrl))
		} else {
			result = append(result, fmt.Sprintf("根据%s的%s %s推荐;", clueCompanyName, typeName, reason.Content))
		}
	}

	return result
}
