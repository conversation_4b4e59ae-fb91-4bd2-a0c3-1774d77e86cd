package utils

func PageBy[T any](l []T, page, size int) []T {
	start, end, ok := SlicePage(page, size, len(l))
	if !ok {
		return nil
	}
	return l[start:end]
}

func SlicePage(page, size, total int) (start, end int, ok bool) {
	page = If(page <= 0, 1, page)
	size = If(size <= 0, 1, size)

	start = (page - 1) * size
	if start >= total {
		return 0, 0, false
	}

	end = start + size
	end = If(end > total, total, end)

	return start, end, true
}

func Page(total, pagePer int) int {
	if total <= 0 {
		return 0
	}

	pagePer = If(pagePer <= 0, 1, pagePer)
	if total <= pagePer {
		return 1
	}
	if total%pagePer != 0 {
		return total/pagePer + 1
	}
	return total / pagePer
}
