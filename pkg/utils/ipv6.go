package utils

import (
	"net"
	"strings"
)

// completeIPV6 将IPv6地址补全为完整格式
// 对应PHP的completeIPV6函数，但进行了性能和错误处理优化
func CompleteIPV6(ipv6 string) string {
	// 如果输入为空，直接返回
	if ipv6 == "" {
		return ipv6
	}

	// 验证是否为有效的IPv6地址
	if !isValidIPv6(ipv6) {
		return ipv6
	}

	// 处理IPv6地址补全
	return expandIPv6(ipv6)
}

// isValidIPv6 检查是否为有效的IPv6地址
func isValidIPv6(ip string) bool {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 检查是否为IPv6（To4()返回nil表示不是IPv4）
	return parsedIP.To4() == nil
}

// expandIPv6 展开IPv6地址为完整格式
func expandIPv6(ipv6 string) string {
	// 处理::压缩表示法
	if strings.Contains(ipv6, "::") {
		ipv6 = expandDoubleColon(ipv6)
	}

	// 分割地址段
	parts := strings.Split(ipv6, ":")

	// 补全每个段为4位十六进制
	for i, part := range parts {
		parts[i] = padHex(part)
	}

	return strings.Join(parts, ":")
}

// expandDoubleColon 展开::压缩表示法
func expandDoubleColon(ipv6 string) string {
	// 分割地址，保留空字符串
	parts := strings.Split(ipv6, "::")
	if len(parts) != 2 {
		return ipv6 // 无效格式
	}

	left := parts[0]
	right := parts[1]

	// 计算左右两边的段数
	leftSegments := 0
	if left != "" {
		leftSegments = strings.Count(left, ":") + 1
	}

	rightSegments := 0
	if right != "" {
		rightSegments = strings.Count(right, ":") + 1
	}

	// 计算需要补充的段数
	missingSegments := 8 - leftSegments - rightSegments

	// 构建补充的零段
	zeroSegments := make([]string, missingSegments)
	for i := range zeroSegments {
		zeroSegments[i] = "0000"
	}

	// 重新组装地址
	var result strings.Builder
	if left != "" {
		result.WriteString(left)
		result.WriteString(":")
	}
	result.WriteString(strings.Join(zeroSegments, ":"))
	if right != "" {
		result.WriteString(":")
		result.WriteString(right)
	}

	return result.String()
}

// padHex 将十六进制字符串补全为4位
func padHex(hex string) string {
	if len(hex) >= 4 {
		return hex
	}

	// 在左侧补0
	return strings.Repeat("0", 4-len(hex)) + hex
}

// CompleteIPV6Batch 批量处理IPv6地址补全
// 新增的批量处理方法，提高大量地址处理的性能
func CompleteIPV6Batch(ipv6List []string) []string {
	if len(ipv6List) == 0 {
		return ipv6List
	}

	results := make([]string, len(ipv6List))

	for i, ipv6 := range ipv6List {
		results[i] = CompleteIPV6(ipv6)
	}

	return results
}

// IsIPv6 检查字符串是否为IPv6地址（包括简写形式）
func IsIPv6(ip string) bool {
	return isValidIPv6(ip)
}

// CompressIPv6 将完整IPv6地址压缩为最短形式（与CompleteIPV6相反）
func CompressIPv6(ipv6 string) string {
	parsedIP := net.ParseIP(ipv6)
	if parsedIP == nil {
		return ipv6
	}

	// 使用Go标准库的IPv6格式化
	return parsedIP.String()
}

// IPv6Utils 提供IPv6相关的工具方法
type IPv6Utils struct{}

// NewIPv6Utils 创建IPv6工具实例
func NewIPv6Utils() *IPv6Utils {
	return &IPv6Utils{}
}

// Complete 实例方法版本的IPv6补全
func (u *IPv6Utils) Complete(ipv6 string) string {
	return CompleteIPV6(ipv6)
}

// Validate 验证IPv6地址
func (u *IPv6Utils) Validate(ipv6 string) bool {
	return isValidIPv6(ipv6)
}

// Compress 压缩IPv6地址
func (u *IPv6Utils) Compress(ipv6 string) string {
	return CompressIPv6(ipv6)
}

// GetNetworkAddress 获取IPv6网络地址
func (u *IPv6Utils) GetNetworkAddress(ipv6 string, prefixLen int) (string, error) {
	ip := net.ParseIP(ipv6)
	if ip == nil {
		return "", &IPv6Error{Message: "invalid IPv6 address", Address: ipv6}
	}

	if ip.To4() != nil {
		return "", &IPv6Error{Message: "not an IPv6 address", Address: ipv6}
	}

	// 创建网络掩码
	mask := net.CIDRMask(prefixLen, 128)

	// 应用掩码
	network := ip.Mask(mask)

	return network.String(), nil
}

// IPv6Error 自定义错误类型
type IPv6Error struct {
	Message string
	Address string
}

func (e *IPv6Error) Error() string {
	return e.Message + ": " + e.Address
}
