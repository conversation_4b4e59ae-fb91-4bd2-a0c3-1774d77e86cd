package utils

import (
	"fmt"
	"net"
	"net/netip"
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"github.com/miekg/dns"
	"golang.org/x/net/idna"
	"golang.org/x/net/publicsuffix"
)

func IsIP(ip string) bool {
	return net.ParseIP(ip) != nil
}

func IsIPv4(ip string) bool {
	if index := strings.IndexByte(ip, '.'); index > 0 {
		return IsIP(ip)
	}
	return false
}

func IsPrivateIP(s string) (isIP bool, isPrivate bool) {
	ip := net.ParseIP(s)
	if ip == nil {
		return false, false
	}
	return true, ip.IsPrivate()
}

func IPExpanded(ipStr string) string {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return ""
	}
	if ip.To4() != nil {
		return ipStr
	}
	v6, _ := netip.ParseAddr(ipStr)
	return v6.StringExpanded()
}

func IsValidatePort(s string) bool {
	port, err := strconv.Atoi(s)
	if err != nil {
		return false
	}
	return port > 0 && port < 65536
}

func IPContains(ips string, target string) bool {
	ipParse := net.ParseIP(target)
	if ipParse == nil {
		return false
	}

	if !strings.Contains(ips, "/") {
		// ips is ipv4 or ipv6
		return ips == target
	}

	// ips is cidr
	_, ipNet, err := net.ParseCIDR(ips)
	if err != nil {
		return false
	}

	return ipNet.Contains(ipParse)
}

// CIDRToIPRange return a list of a CIDR
func CIDRToIPRange(s string) ([]string, error) {
	parsedIP, ipNet, err := net.ParseCIDR(s)
	if err != nil {
		return nil, err
	}

	inc := func(ip net.IP) {
		for j := len(ip) - 1; j >= 0; j-- {
			ip[j]++
			if ip[j] > 0 {
				break
			}
		}
	}

	start, end := ipNet.Mask.Size()
	if start < 1 || end > 128 {
		return nil, fmt.Errorf("invalid CIDR address: %s", s)
	}

	var ips = make([]string, 0, 2<<(end-start))
	for ip := parsedIP.Mask(ipNet.Mask); ipNet.Contains(ip); inc(ip) {
		ips = append(ips, ip.String())
	}

	return ips, nil
}

var domainRegexp = regexp.MustCompile(`(([[:alnum:]]-?)?([[:alnum:]]-?)+\.)+[[:alpha:]]{2,4}`)

func DomainFromUrl(url string) string {
	return domainRegexp.FindString(url)
}

func FindRootDomain(domain string) (string, error) {
	if index := strings.Index(domain, "://"); index >= 0 {
		domain = domain[index+3:]
	}

	if ip := net.ParseIP(domain); ip != nil {
		return "", fmt.Errorf("%s is a invalid domain", domain)
	}

	root, err := publicsuffix.EffectiveTLDPlusOne(domain)
	if err != nil {
		return "", err
	}
	return root, nil
}

func IsRootDomain(domain string) bool {
	d, err := FindRootDomain(domain)
	return d == domain && err == nil
}

// IPv6DomainReplace
//
// https://[::1]:8080/fjasokfji/resource
func IPv6DomainReplace(domain string) string {
	// [::1]:80
	head := strings.Index(domain, "[")
	tail := strings.Index(domain, "]")
	if head == -1 || tail == -1 || head >= tail {
		return domain
	}

	first := domain[:head]
	f := strings.ToLower(IPExpanded(domain[head+1 : tail]))
	domain = "[" + f + "]" + domain[tail+1:]
	return first + domain
}

// GetDNSARecords 获取域名的A记录
func GetDNSARecords(domain string) ([]string, error) {
	var result []string
	c := new(dns.Client)
	m := new(dns.Msg)
	m.SetQuestion(dns.Fqdn(domain), dns.TypeA)
	m.RecursionDesired = true
	r, _, err := c.Exchange(m, "*******:53") // 使用Google DNS
	if err != nil {
		return nil, err
	}
	if r.Rcode != dns.RcodeSuccess {
		return nil, fmt.Errorf("DNS query failed with code: %d", r.Rcode)
	}
	for _, ans := range r.Answer {
		if a, ok := ans.(*dns.A); ok {
			result = append(result, a.A.String())
		}
	}
	return result, nil
}

// GetDNSAAAARecords 获取域名的AAAA记录
func GetDNSAAAARecords(domain string) ([]string, error) {
	var result []string
	// 使用dns库查询DNS
	c := new(dns.Client)
	m := new(dns.Msg)
	m.SetQuestion(dns.Fqdn(domain), dns.TypeAAAA)
	m.RecursionDesired = true
	r, _, err := c.Exchange(m, "*******:53") // 使用Google DNS
	if err != nil {
		return nil, err
	}
	if r.Rcode != dns.RcodeSuccess {
		return nil, fmt.Errorf("DNS query failed with code: %d", r.Rcode)
	}
	for _, ans := range r.Answer {
		if a, ok := ans.(*dns.AAAA); ok {
			result = append(result, a.AAAA.String())
		}
	}
	return result, nil
}

// CompleteIPv6 补全IPv6格式
func CompleteIPv6(ipStr string) string {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return ""
	}
	return ip.String()
}

// IsValidDomainStrict 严格验证字符串是否为有效域名
func IsValidDomainStrict(domain string) bool {
	// 检查长度限制
	if len(domain) < 1 || len(domain) > 253 {
		return false
	}

	// 检查是否全为ASCII字符
	isASCII := true
	for _, r := range domain {
		if r > unicode.MaxASCII {
			isASCII = false
			break
		}
	}

	// 如果是ASCII域名，使用常规验证
	if isASCII {
		return validateASCIIDomain(domain)
	}

	// 如果是国际化域名，尝试转换为Punycode后验证
	punycode, err := idna.ToASCII(domain)
	if err != nil {
		return false
	}
	return validateASCIIDomain(punycode)
}

// validateASCIIDomain 验证ASCII格式的域名
func validateASCIIDomain(domain string) bool {
	// 检查是否包含有效字符
	for _, c := range domain {
		if !(c >= 'a' && c <= 'z' || c >= 'A' && c <= 'Z' || c >= '0' && c <= '9' || c == '-' || c == '.') {
			return false
		}
	}

	// 分割标签
	labels := strings.Split(domain, ".")
	if len(labels) < 2 { // 至少要有二级域名
		return false
	}

	// 检查每个标签
	for _, label := range labels {
		// 标签长度检查
		if len(label) < 1 || len(label) > 63 {
			return false
		}

		// 连字符不能在开头或结尾
		if label[0] == '-' || label[len(label)-1] == '-' {
			return false
		}

		// TLD不能全是数字
		if label == labels[len(labels)-1] {
			if isAllDigits(label) {
				return false
			}
		}
	}

	return true
}

// isAllDigits 检查字符串是否全为数字
func isAllDigits(s string) bool {
	for _, c := range s {
		if c < '0' || c > '9' {
			return false
		}
	}
	return true
}
