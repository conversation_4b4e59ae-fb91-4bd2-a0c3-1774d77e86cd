package utils

import (
	"reflect"
	"strconv"
	"strings"
	"time"

	"golang.org/x/exp/constraints"
)

func ListNonZero[T constraints.Ordered](l []T) []T {
	var empty T
	var newList = make([]T, 0, len(l))
	for i := range l {
		if l[i] != empty {
			newList = append(newList, l[i])
		}
	}
	return newList
}

func ListDistinct[T constraints.Ordered](l []T, filterZero ...bool) []T {
	if len(l) <= 1 {
		return l
	}

	var empty T
	isFilter := ListFirstEle(filterZero)
	set := make(map[T]struct{}, len(l))
	newList := make([]T, 0, len(l))
	for i := range l {
		if _, ok := set[l[i]]; !ok {
			set[l[i]] = struct{}{}
			if !isFilter {
				newList = append(newList, l[i])
			} else if isFilter && l[i] != empty {
				newList = append(newList, l[i])
			}
		}
	}
	return newList
}

func ListDistinctNonZero[T constraints.Ordered](l []T) []T {
	var empty T
	set := make(map[T]struct{}, len(l))
	newList := make([]T, 0, len(l))
	for i := range l {
		if _, ok := set[l[i]]; !ok && l[i] != empty {
			set[l[i]] = struct{}{}
			newList = append(newList, l[i])
		}
	}
	return newList
}

func ListContains[T constraints.Ordered](l []T, ele T) bool {
	for _, v := range l {
		if v == ele {
			return true
		}
	}
	return false
}

// ListFilter 根据条件函数过滤列表
func ListFilter[T any](list []T, condition func(T) bool) []T {
	var result []T
	for _, item := range list {
		if condition(item) {
			result = append(result, item)
		}
	}
	return result
}

// StructFilter 根据结构体字段条件过滤结构体切片
func StructFilter[T any](arr []T, conditions map[string]interface{}) []T {
	sliceValue := reflect.ValueOf(arr)
	result := reflect.MakeSlice(sliceValue.Type(), 0, sliceValue.Len())

	for i := 0; i < sliceValue.Len(); i++ {
		elem := sliceValue.Index(i)
		// 处理指针类型
		var structVal reflect.Value
		switch elem.Kind() {
		case reflect.Ptr:
			if elem.IsNil() {
				continue
			}
			structVal = elem.Elem()
			if structVal.Kind() != reflect.Struct {
				continue
			}
		case reflect.Struct:
			structVal = elem
		default:
			continue
		}
		// 检查字段匹配
		match := true
		for name, expected := range conditions {
			field := structVal.FieldByName(name)
			if !field.IsValid() || field.Type() != reflect.TypeOf(expected) {
				match = false
				break
			}
			// 安全比较
			func() {
				defer func() {
					if r := recover(); r != nil {
						match = false
					}
				}()
				if !field.Equal(reflect.ValueOf(expected)) {
					match = false
				}
			}()
			if !match {
				break
			}
		}
		if match {
			result = reflect.Append(result, elem)
		}
	}
	return result.Interface().([]T)
}

func ListStrContains[T ~string](l []T, substr T) bool {
	for _, v := range l {
		if strings.Contains(string(v), string(substr)) {
			return true
		}
	}
	return false
}

func ListFunc[T, T2 any](list []T, fn func(T) (T2, bool)) (result []T2, affected int) {
	for i := range list {
		s, ok := fn(list[i])
		if ok {
			result = append(result, s)
			continue
		}
		affected++
	}

	return
}

func ListFirstEle[T any](l []T) T {
	var s T
	if len(l) > 0 {
		s = l[0]
	}
	return s
}

func ListDelete[T constraints.Ordered](l []T, val ...T) (t []T, affected int) {
	if len(l) == 0 {
		return l, 0
	}

	for i := range l {
		store := true
		for i2 := range val {
			if l[i] == val[i2] {
				store = false
				affected++
				continue
			}
		}
		if store {
			t = append(t, l[i])
		}
	}
	return t, affected
}

func ListReplace[T constraints.Ordered](l []T, oldVal, newVal T) []T {
	if len(l) == 0 || oldVal == newVal {
		return l
	}

	for i := range l {
		if l[i] == oldVal {
			l[i] = newVal
		}
	}
	return l
}

func ListMerge[T any](l1, l2 []T) []T {
	var dst = make([]T, len(l1)+len(l2))
	copy(dst, l1)
	copy(dst[len(l1):], l2)
	return dst
}

// ListUnion
// 并集
func ListUnion[T constraints.Ordered](A, B []T) []T {
	A = append(A, B...)
	return ListDistinct(A)
}

// ListIntersect 交集
func ListIntersect[T constraints.Ordered](A, B []T) []T {
	if len(A) == 0 || len(B) == 0 {
		return nil
	}
	var newList = make([]T, 0, len(A))
	for i := range A {
		for i2 := range B {
			if A[i] == B[i2] {
				newList = append(newList, A[i])
				break
			}
		}
	}
	return newList
}

// ListColumn 提取字段
func ListColumn[E any, T any](l []T, getField func(T) E) []E {
	sl := make([]E, 0, len(l))
	for i := range l {
		sl = append(sl, getField(l[i]))
	}
	return sl
}

// ListComplementary return A-B(补集)
// 补集: 仅存在于A集合的元素
func ListComplementary[T constraints.Ordered](A, B []T) (list []T) {
	if len(A) == 0 || len(B) == 0 {
		return A
	}
	for _, v := range A {
		ok := true
		for _, v2 := range B {
			if v == v2 {
				ok = false
				break
			}
		}
		if ok {
			list = append(list, v)
		}
	}
	return list
}

// ListItoa return a string list of integer list
func ListItoa[T constraints.Signed](l []T) []string {
	sl := make([]string, 0, len(l))
	for i := range l {
		sl = append(sl, strconv.FormatInt(int64(l[i]), 10))
	}
	return sl
}

func ListUItoa[T constraints.Unsigned](l []T) []string {
	sl := make([]string, 0, len(l))
	for i := range l {
		sl = append(sl, strconv.FormatUint(uint64(l[i]), 10))
	}
	return sl
}

func ListToSet[T comparable](list []T) map[T]struct{} {
	return ListToSetFunc(list, func(a T) (T, bool) { return a, true })
}

func ListToMapFunc[T comparable, V any](l []V, f func(v V) (key T, ok bool)) map[T]V {
	m := make(map[T]V, len(l))
	for i := range l {
		if key, b := f(l[i]); b {
			m[key] = l[i]
		}
	}
	return m
}

func ListToSetFunc[T comparable, V any](l []V, f func(v V) (key T, ok bool)) map[T]struct{} {
	m := make(map[T]struct{}, len(l))
	for i := range l {
		if key, b := f(l[i]); b {
			m[key] = struct{}{}
		}
	}
	return m
}

func ListSplit[T any](arr []T, size int) [][]T {
	if size <= 0 {
		size = 1
	}
	var result [][]T
	length := len(arr)
	for i := 0; i < length; i += size {
		end := i + size
		if end > length {
			end = length
		}
		result = append(result, arr[i:end])
	}
	return result
}

func ListFirstNonZero[T constraints.Ordered](l []T) T {
	var null T
	for i := range l {
		if l[i] != null {
			return l[i]
		}
	}
	return null
}

func GetMapKeys[T comparable, V any](m map[T]V) []T {
	keys := make([]T, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func GetMapValues[T comparable, V any](m map[T]V) []V {
	values := make([]V, 0, len(m))
	for _, v := range m {
		values = append(values, v)
	}
	return values
}

func StringsToUints64[T ~string](l []T) []uint64 {
	var ints64 []uint64
	for _, v := range l {
		iv, err := strconv.ParseUint(string(v), 10, 64)
		if err == nil {
			ints64 = append(ints64, iv)
		}
	}
	return ints64
}

// StringsToTime 字符串切片转为时间格式的字符串切片。
// 传入的字符串切片长度为0时，返回空切片。
// 如果传入的切片元素为空字符串或者传入的内容不是日期格式，则该元素被忽略。
// 传入的字符串切片长度为1时，如果包含":"，则在末尾加上"00:00:00"，否则直接返回。
// 传入的字符串切片长度为2时，如果包含":"，则在第一个元素末尾加上"00:00:00"，在第二个元素末尾加上"23:59:59"，否则直接返回。
// 仅处理切片的前两个元素，多余的元素会被忽略。
func FormattingTime(l []string) []string {
	if len(l) == 0 {
		return l
	}
	times := make([]string, 0, len(l))
	if len(l) == 1 {
		if _, err := time.Parse("2006-01-02", l[0]); err == nil {
			if !strings.Contains(l[0], ":") {
				times = append(times, l[0]+" 00:00:00")
			} else {
				times = append(times, l[0])
			}
		}
	}
	if len(l) > 1 {
		if _, err := time.Parse("2006-01-02", l[0]); err == nil {
			if !strings.Contains(l[0], ":") {
				times = append(times, l[0]+" 00:00:00")
			} else {
				times = append(times, l[0])
			}
		}
		if _, err := time.Parse("2006-01-02", l[1]); err == nil {
			if !strings.Contains(l[1], ":") {
				if len(times) == 0 {
					times = append(times, l[1]+" 00:00:00")
				} else {
					times = append(times, l[1]+" 23:59:59")
				}
			} else {
				times = append(times, l[1])
			}
		}
	}
	return times
}

func Contains[T comparable](arr []T, target T) bool {
	for _, item := range arr {
		if item == target {
			return true
		}
	}
	return false
}

// ListContainsAny 检查一个任意类型的切片是否包含指定元素（通过字符串比较）
func ListContainsAny(l []interface{}, ele interface{}) bool {
	eleStr := AnyToStr(ele)
	for _, v := range l {
		if AnyToStr(v) == eleStr {
			return true
		}
	}
	return false
}
