package domain

import (
	"context"
	"fmt"
	"math/rand"
	"micro-service/middleware/redis"
	"micro-service/pkg/utils"
	"net"
	"time"
)

// IsWildcardDomain 判断一个域名是不是泛解析域名
// 传进来的可能是子域名也可能是主域名，会自动提取主域名进行检测
// 结果会缓存15天
func IsWildcardDomain(domain string) bool {
	if domain == "" {
		return false
	}

	// 获取主域名（如果传入的是子域名）
	topDomain := utils.GetTopDomain(domain)
	if topDomain == "" {
		return false
	}

	// 构建缓存key
	cacheKey := fmt.Sprintf("foradar_cache:wildcard_domain:%s", topDomain)

	// 先检查缓存
	var cached bool
	if getCacheResult(cacheKey, &cached) {
		return cached
	}

	// 执行泛解析检测
	isWildcard := checkWildcardDomain(topDomain)

	// 缓存结果15天
	setCacheResult(cacheKey, isWildcard, 15*24*time.Hour)

	return isWildcard
}

// checkWildcardDomain 检测域名是否为泛解析域名
func checkWildcardDomain(domain string) bool {
	// 生成多个随机子域名进行测试，提高准确性
	testCount := 3
	successCount := 0
	var testResults []string

	for i := 0; i < testCount; i++ {
		// 生成随机前缀的测试域名，确保是不存在的随机子域名
		randomNum := rand.Int63n(21323213213-1000123+1) + 1000123
		randomPrefix := fmt.Sprintf("nonexistent%s%d%d", utils.Md5Hash(domain), randomNum, i)
		testDomain := fmt.Sprintf("%s.%s", randomPrefix, domain)

		// 尝试DNS解析
		canResolveResult := canResolve(testDomain)
		if canResolveResult {
			successCount++
		}

		// 记录测试结果用于调试
		testResults = append(testResults, fmt.Sprintf("%s:%v", testDomain, canResolveResult))
	}

	// 如果多数随机子域名都能解析成功，则认为是泛解析域名
	// 正常情况下，随机的不存在的子域名应该解析失败
	// 如果能解析成功，说明该域名配置了泛解析
	isWildcard := successCount >= 2

	// 添加详细的日志记录
	fmt.Printf("[DEBUG] 泛解析检测 - 域名: %s, 成功解析: %d/%d, 测试详情: %v, 结果: %v\n",
		domain, successCount, testCount, testResults, isWildcard)

	return isWildcard
}

// canResolve 检查域名是否可以解析
func canResolve(domain string) bool {
	// 设置超时时间为3秒
	timeout := 3 * time.Second

	// 创建自定义的Resolver，设置超时时间
	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: timeout,
			}
			return d.DialContext(ctx, network, address)
		},
	}

	// 创建带超时的context
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// 尝试DNS解析A记录
	ips, err := resolver.LookupIPAddr(ctx, domain)
	if err == nil && len(ips) > 0 {
		return true
	}

	return false
}

// getCacheResult 获取缓存结果
func getCacheResult(key string, result interface{}) bool {
	return redis.GetCache(key, result)
}

// setCacheResult 设置缓存结果
func setCacheResult(key string, value interface{}, ttl time.Duration) bool {
	return redis.SetCache(key, ttl, value)
}
