package process

import (
	"fmt"
	"strconv"
	"sync"
	"time"
)

type Bar struct {
	mu      sync.Mutex
	graph   string    // 显示符号
	rate    string    // 进度条
	Percent int       // 百分比
	Current int       // 当前进度位置
	Total   int       // 总进度
	StartAt time.Time // 开始时间
}

func NewBar(current, total int) *Bar {
	bar := new(Bar)
	bar.Current = current
	bar.Total = total
	bar.StartAt = time.Now()
	if bar.graph == "" {
		bar.graph = "█"
	}
	bar.Percent = bar.GetPercent()
	for i := 0; i < bar.Percent; i += 2 {
		bar.rate += bar.graph // 初始化进度条位置
	}
	return bar
}

func NewBarWithGraph(start, total int, graph string) *Bar {
	bar := NewBar(start, total)
	bar.graph = graph
	return bar
}

func (bar *Bar) GetPercent() int {
	return int((float64(bar.Current) / float64(bar.Total)) * 100)
}

func (bar *Bar) GetTime() (s string) {
	u := time.Now().Sub(bar.StartAt).Seconds()
	h := int(u) / 3600
	m := int(u) % 3600 / 60
	if h > 0 {
		s += strconv.Itoa(h) + "h "
	}
	if h > 0 || m > 0 {
		s += strconv.Itoa(m) + "m "
	}
	s += strconv.Itoa(int(u)%60) + "s"
	return
}

func (bar *Bar) printf() {
	fmt.Printf("\r[%-50s]% 3d%%    %2s   %d/%d", bar.rate, bar.Percent, bar.GetTime(), bar.Current, bar.Total)
}

func (bar *Bar) Reset(f func()) {
	bar.mu.Lock()
	defer bar.mu.Unlock()
	bar.Current = 0
	last := bar.Percent
	bar.Percent = bar.GetPercent()
	if bar.Percent != last && bar.Percent%2 == 0 {
		bar.rate += bar.graph
	}
	f()
}

func (bar *Bar) Add(i int, f func()) {
	bar.mu.Lock()
	defer bar.mu.Unlock()
	bar.Current += i
	last := bar.Percent
	bar.Percent = bar.GetPercent()
	if bar.Percent != last && bar.Percent%2 == 0 {
		bar.rate += bar.graph
	}
	f()
}
func (bar *Bar) Finish(f func()) {
	bar.mu.Lock()
	defer bar.mu.Unlock()
	bar.Current = bar.Total
	last := bar.Percent
	bar.Percent = bar.GetPercent()
	if bar.Percent != last && bar.Percent%2 == 0 {
		bar.rate += bar.graph
	}
	f()
}
