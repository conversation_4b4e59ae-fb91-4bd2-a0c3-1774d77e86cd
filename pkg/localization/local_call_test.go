package localization

//func Test_CoreFofaQuery(t *testing.T) {
//	cfg.InitLoadCfg()
//	log.Init()
//	redis.GetInstance(cfg.LoadRedis())
//
//	caller := NewCaller()
//	rsp := &core.FofaQueryResponse{}
//	err := caller.CoreFofaQuery(&core.FofaQueryRequest{
//		Qbase64: `banner = "dell"`,
//		Full:    false,
//		Field:   []string{"ip"},
//		Page:    1,
//		Size:    10,
//	}, rsp)
//	assert.Nil(t, err)
//}
//
//func Test_tianyancha_through(t *testing.T) {
//
//	cfg.InitLoadCfg()
//	log.Init()
//	redis.GetInstance(cfg.LoadRedis())
//
//	var rsp = new(core.QCCInvestmentThroughResponse)
//	var companyName = "北京华顺信安科技有限公司"
//	var req = &core.QCCInvestmentThroughRequest{Search: companyName}
//	//rsp, _ = core.GetProtoCoreClient().QCCInvestmentThrough(context.Background(), req)
//	//fmt.Println(rsp)
//
//	caller := NewCaller()
//	err := caller.CoreQCCInvestmentThrough(req, rsp)
//
//	if err != nil {
//		assert.NotEmpty(t, rsp)
//	}
//}
