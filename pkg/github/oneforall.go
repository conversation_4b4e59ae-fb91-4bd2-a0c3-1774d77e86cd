package github

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"micro-service/middleware/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// mockExecOneForAllWrapper 允许测试时注入 mock
var mockExecOneForAllWrapper = execOneForAll

// getOneForAllPath 获取OneForAll目录的绝对路径
func getOneForAllPath() (string, error) {
	// 获取当前文件的路径
	_, currentFile, _, ok := runtime.Caller(0)
	if !ok {
		return "", fmt.Errorf("无法获取当前文件路径")
	}

	// 当前文件在 pkg/github/oneforall.go
	// OneForAll目录在 pkg/github/OneForAll/
	currentDir := filepath.Dir(currentFile)
	oneForAllPath := filepath.Join(currentDir, "OneForAll")

	// 检查目录是否存在
	if _, err := os.Stat(oneForAllPath); os.IsNotExist(err) {
		return "", fmt.Errorf("OneForAll目录不存在: %s", oneForAllPath)
	}

	return oneForAllPath, nil
}

func OneForAllQuery(ctx context.Context, domain string, title string, bruteforce bool) ([]map[string]string, error) {
	var options strings.Builder

	if title != "" {
		options.WriteString(fmt.Sprintf(" --title=%s ", title))
	}
	if bruteforce {
		options.WriteString(" --bruteforce ")
	}

	// 匹配中文过滤
	if utils.CheckIdnDomain(domain) {
		domain = utils.GetIdnDomain(domain, false)
	}

	// 尝试从Redis获取缓存的数据
	cacheKey := fmt.Sprintf("oneforall:%s", domain)
	var cachedData []map[string]string
	if redis.Get(ctx, cacheKey, &cachedData) == nil {
		log.Debugf("OneForAll-取的缓存: domain=%s", domain)
		return cachedData, nil
	}

	// 获取OneForAll目录的绝对路径
	oneForAllPath := cfg.LoadCommon().OneForAllPath
	if oneForAllPath == "" {
		path, err := getOneForAllPath()
		if err != nil {
			log.Errorf("OneForAll-获取OneForAll路径失败: %v", err)
			return nil, err
		}
		oneForAllPath = path
	}
	// 检查目录是否存在
	if _, err := os.Stat(oneForAllPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("OneForAll目录不存在: %s", oneForAllPath)
	}

	log.Debugf("OneForAll-尝试从文件读取结果")
	// 有缓存取缓存数据
	resultsPath := filepath.Join(oneForAllPath, "results")
	fullFilename := filepath.Join(resultsPath, fmt.Sprintf("%s.csv", domain))

	// 获取文件的修改时间
	fileInfo, err := os.Stat(fullFilename)
	var fileModifiedTime time.Time
	fileExists := false
	if err == nil {
		fileModifiedTime = fileInfo.ModTime()
		fileExists = true
	}

	// 检查文件是否存在以及是否在7天前修改
	sevenDaysAgo := time.Now().Add(-7 * 24 * time.Hour)
	if !fileExists || (fileExists && fileModifiedTime.Before(sevenDaysAgo)) {
		// 如果文件不存在或者在7天前修改，那么删除文件（如果存在）
		if fileExists {
			log.Debugf("OneForAll-删除之前的任务结果记录: domain=%s, fullFilename=%s", domain, fullFilename)
			os.Remove(fullFilename)
		}

		output, err := mockExecOneForAllWrapper(ctx, domain, oneForAllPath)
		if err != nil {
			log.Errorf("OneForAll命令执行失败: domain=%s, error=%v, output=%s", domain, err, string(output))
		} else {
			log.Debugf("OneForAll -> %s: %s", domain, string(output))
		}
		log.Infof("OneForAll命令执行完成，domain:%s", domain)
	}

	data, err := getCsvData(fullFilename)
	if err != nil {
		log.Errorf("OneForAll CSV解析失败: domain=%s, error=%v", domain, err)
		return nil, err
	}

	// 缓存结果到Redis，设置有效时间为7天
	if len(data) > 0 {
		ttl := 7 * 24 * time.Hour
		jsonData, err := json.Marshal(data)
		if err != nil {
			log.Errorf("OneForAll JSON序列化失败: domain=%s, error=%v", domain, err)
		}
		if err := redis.Set(ctx, cacheKey, string(jsonData), ttl); err != nil {
			log.Errorf("OneForAll Redis缓存存储失败: domain=%s", domain)
		}
	}

	return data, nil
}

// execOneForAll 执行OneForAll
func execOneForAll(ctx context.Context, domain string, oneForAllPath string) ([]byte, error) {
	log.Infof("OneForAll-执行脚本获取结果,domain:%s,path:%s", domain, oneForAllPath)
	// 执行OneForAll Python命令
	pythonPath := cfg.LoadCommon().PythonPath
	filePath := filepath.Join(oneForAllPath, "oneforall.py")
	cmdStr := fmt.Sprintf("%s python3 %s --target %s run", pythonPath, filePath, domain)

	// 设置超时
	timeout := 7200 * time.Second
	cmdCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	cmd := exec.CommandContext(cmdCtx, "bash", "-c", cmdStr)
	cmd.Dir = oneForAllPath

	// 执行命令并处理输出
	output, err := cmd.CombinedOutput()
	return output, err
}

// getCsvData 解析CSV文件并返回结构化数据
func getCsvData(filePath string) ([]map[string]string, error) {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return []map[string]string{}, nil
	}

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开CSV文件失败: %v", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("读取CSV文件失败: %v", err)
	}

	if len(records) == 0 {
		return []map[string]string{}, nil
	}

	// 第一行作为header
	headers := records[0]
	var data []map[string]string

	// 从第二行开始解析数据
	for i := 1; i < len(records); i++ {
		row := records[i]
		rowMap := make(map[string]string)

		// 将每一列数据与header对应
		for j, value := range row {
			if j < len(headers) {
				rowMap[headers[j]] = value
			}
		}
		data = append(data, rowMap)
	}

	return data, nil
}
