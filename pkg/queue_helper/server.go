package queue_helper

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"os"
	"runtime/debug"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	goRedis "github.com/go-redis/redis/v8"
)

var handlers = make(map[string]func(context.Context, *Task) error)
var srv *Server
var once sync.Once
var queueName = "foradar:asyncq"
var logKey = "foradar:consume:log"

func Start(service string) {
	redisCfg := cfg.LoadRedis()
	log.Infof("[asyncq]redisCfg:%+v", redisCfg)
	redisClient := redis.GetInstance(redisCfg)
	srv = &Server{
		Redis:    redisClient,
		Cfg:      redisCfg,
		Chan:     make(chan *Task, redisCfg.AsyncQueueSize),
		StopExec: make(chan struct{}, 1),
		StopSub:  make(chan struct{}, 1),
	}
	RegisterConsumer(service)
	go srv.Sub()
	go srv.Exec()
}

func (s *Server) Sub() {
	for {
		select {
		case <-s.StopSub:
			log.Info("[asyncq]stop sub")
			return
		default:
			val, err := s.Redis.BLPop(context.TODO(), 3*time.Second, queueName).Result()
			if err != nil && !errors.Is(err, goRedis.Nil) {
				log.Errorf("[asyncq]could not get task from redis: %v", err)
				// 如果是连接关闭的错误，则等待3秒后重连
				if errors.Is(err, goRedis.ErrClosed) || strings.Contains(err.Error(), "timeout") {
					time.Sleep(time.Second * 3)
				}
				continue
			}
			if len(val) == 0 {
				continue
			}
			lk := logKey + ":" + time.Now().Format("01:02")
			hostname, _ := os.Hostname()
			s.Redis.LPush(context.TODO(), lk, utils.AnyToStr(map[string]interface{}{
				"time": time.Now().Format(time.DateTime),
				"host": hostname,
				"val":  val[1],
			}))
			s.Redis.Expire(context.TODO(), lk, 48*time.Hour)
			var t *Task
			err = json.Unmarshal([]byte(val[1]), &t)
			if err != nil {
				log.Errorf("[asyncq]could not unmarshal task: %v, val:%s", err, val)
				continue
			}
			if t == nil || t.Type == "" || t.Payload == "" {
				log.Errorf("[asyncq]task is nil, val:%s", val[1])
				continue
			}
			srv.Chan <- t
			time.Sleep(time.Second * 3)
		}
	}
}

func (s *Server) Exec() {
	// redis 队列池设置50
	var pool = make(chan struct{}, s.Cfg.AsyncQueueSize)
	poolLength := atomic.Int32{}
	log.Infof("[asyncq]pool capacity:%d", s.Cfg.AsyncQueueSize)
	for {
		select {
		case <-s.StopExec:
			log.Info("[asyncq]stop exec")
			return
		case t := <-s.Chan:
			go func(t *Task) {
				defer func() {
					<-pool
					poolLength.Add(-1)
					log.Infof("[asyncq]current pool length:%d", poolLength.Load())
					if r := recover(); r != nil {
						log.Errorf("[asyncq]task:%s,type:%s,panic:%v", t.Payload, t.Type, r)
						log.Errorf("[asyncq]stack:%s", string(debug.Stack()))
						// requeue
						s.Redis.LPush(context.TODO(), queueName, utils.AnyToStr(t))
					} else {
						log.Infof("[asyncq]task:%s,type:%s,success", t.Payload, t.Type)
					}
				}()
				pool <- struct{}{}
				poolLength.Add(1)
				log.Infof("[asyncq]type:%s,start,current pool length:%d", t.Type, poolLength.Load())
				if h, ok := handlers[t.Type]; ok {
					err := h(context.Background(), t)
					if err != nil {
						log.Errorf("[asyncq]task:%s,type:%s,error:%v", t.Payload, t.Type, err)
					}
				}
			}(t)
		}

	}
}

func AddHandler(taskType string, handler func(context.Context, *Task) error) {
	handlers[taskType] = handler
}

func CloseServer(service string) {
	if srv != nil {
		UnregisterConsumer(service)
		srv.StopSub <- struct{}{}
		srv.StopExec <- struct{}{}
	}
}

// 注册自身，可以看到每个服务实例启动与关闭的日志
func RegisterConsumer(service string) {
	// 获取当前IP和hostname
	clientInfo, key := getClientInfo(service)
	jsonClientInfo, _ := json.Marshal(clientInfo)
	// 写入到redis
	err := redis.GetInstance().Set(context.TODO(), key, jsonClientInfo, 1*time.Minute).Err()
	if err != nil {
		log.Errorf("[asyncq]register consumer error: %v", err)
	}

	// 定时更新redis,防止服务强制关闭不更新在线状态
	go func() {
		for {
			time.Sleep(time.Second * 20)
			clientInfo["last"] = time.Now().Format(time.DateTime)
			jsonClientInfo, _ = json.Marshal(clientInfo)
			err := redis.GetInstance().Set(context.TODO(), key, jsonClientInfo, 1*time.Minute).Err()
			if err != nil {
				log.Errorf("[asyncq]update consumer error: %v", err)
			}
		}
	}()
}

// 反注册
func UnregisterConsumer(service string) {
	_, key := getClientInfo(service)
	err := redis.GetInstance().Del(context.TODO(), key).Err()
	if err != nil {
		log.Errorf("[asyncq]unregister consumer error: %v", err)
	}
}

// 获取当前服务实例的唯一标识
func getClientInfo(service string) (map[string]interface{}, string) {
	ip := utils.GetNetworkIp(cfg.LoadCommon().Network)
	hn, _ := os.Hostname()
	uniqueId := fmt.Sprintf("%s.%s", ip, hn)
	uniqueId = utils.Md5Hash(uniqueId)
	now := time.Now()
	return map[string]interface{}{
		"ip":      ip,
		"hn":      hn,
		"start":   now.Format(time.DateTime),
		"last":    now.Format(time.DateTime),
		"service": service,
	}, fmt.Sprintf("client:%s:%s", service, uniqueId)
}
