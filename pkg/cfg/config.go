package cfg

import (
	"fmt"
	"log"

	"go-micro.dev/v4/config"
	"go-micro.dev/v4/config/source/env"
	"go-micro.dev/v4/config/source/file"
	"os"
	"path/filepath"
	"sync"
)

type Cfg struct {
	// Consul Consul配置
	Consul Consul `json:"consul"`
	// Redis redis配置
	Redis Redis `json:"redis"`
	// MySql mysql配置
	MySql MySql `json:"mysql"`
	// RabbitMq mysql配置
	RabbitMq RabbitMq `json:"rabbitmq"`
	// ElasticSearch ElasticSearch配置
	ElasticSearch ElasticSearch `json:"elastic"`
	// Common 通用配置
	Common Common `json:"common"`
	// Logger 日志配置
	Logger Logger `json:"logger"`
	// ShadowSocks SocksVPN配置
	ShadowSocks ShadowSocks `json:"shadowsocks"`
	// Fofa fofa账号配置列表
	Fofa []Fofa `json:"fofa_account_list"`
	// qichacha账号配置
	QiChaCha QCC `json:"qichacha"`
	// TianYanCha 天眼查账号配置
	Tianyancha TYC `json:"tianyancha"`
	// chinaz yoken
	Chinaz Chinaz `json:"chinaz"`
	// microkernel 微内核配置
	Microkernel Microkernel `json:"microkernel"`
	// 00信安 配置
	ZeroZone ZZ        `json:"zero_zone"`
	APP      APPConfig `json:"app"`
	// Hunter hunter账号配置列表
	Hunter Hunter `json:"hunter"`
	// Quake quake账号配置列表
	Quake Quake `json:"quake"`
}

var (
	once               sync.Once
	cfgLock            sync.RWMutex
	singleInstanceConf *Cfg
)

func GetInstance() *Cfg {
	cfgLock.Lock()
	defer cfgLock.Unlock()
	if singleInstanceConf == nil {
		// 加载所有配置
		once.Do(func() { singleInstanceConf = loadCfg() })
	}
	return singleInstanceConf
}

func onInitPath() {

	pwd, err := os.Getwd()
	if err != nil {
		panic(err)
	}

	paths := []string{
		"/etc/foradar/conf/config.json",
		"./conf/config.json",
		"../conf/config.json",
		"../../conf/config.json",
		"../../../conf/config.json",
		"../../../../conf/config.json",
		filepath.Dir(pwd) + "/conf/config.json",
	}

	var configPath string
	for i := range paths {
		if _, err := os.Stat(paths[i]); err == nil {
			configPath = paths[i]
			break
		}
	}
	log.Printf("consul load config file path : %s", configPath)
	// 加载配置文件
	_ = config.Load(file.NewSource(file.WithPath(configPath)))
}

func InitLoadCfg() {
	GetInstance()
}

func pf(name string, err error) string {
	return fmt.Sprintf("load %s config failed: %v", name, err)
}

func loadCfg() *Cfg {
	conf := &Cfg{}
	// 读取本地配置文件，这个是加载服务器上的 config.json文件的配置，这个配置读取以后加载到gomicro的全局配置中
	onInitPath()

	// 读取ENV配置，这个是读取环境变量（go写的应用被docker起来以后，会读取docker-compose.yaml里面设置的environment的值。CONSUL_ADDRESS 等变量就读取进来了）
	// 可以打印config里面已经加载到的配置值
	// 你可以使用config.Map()函数获取所有已加载的配置，并将其打印出来。以下是一个示例，它会打印出所有已加载的配置。config.Map()
	_ = config.Load(env.NewSource())

	// 加载consul配置
	// 环境变量 CONSUL_ADDRESS 对应于配置的 Key consul.address，环境变量 CONSUL_PORT 对应于配置的 Key consul.port。
	// 当你调用 config.Get("consul").Scan(&conf.Consul)，Go Micro 将会查找所有以 consul.
	// 开头的配置项，并尝试将它们的值赋给 conf.Consul 结构体的对应字段。在这个过程中，consul.address 对应于 conf.Consul.Address，consul.port 对应于 conf.Consul.Port。
	if err := config.Get("consul").Scan(&conf.Consul); err != nil {
		panic(pf("consul", err))
	}

	log.Printf("consul config value : %+v", conf.Consul)
	// 加载consul配置中心
	OnInitConfigCenter(conf.Consul)

	// 加载Redis配置
	if err := config.Get("redis").Scan(&conf.Redis); err != nil {
		panic(pf("redis", err))
	}
	// 加载Mysql配置
	if err := config.Get("mysql").Scan(&conf.MySql); err != nil {
		panic(pf("mysql", err))
	}
	// 加载Elastic配置
	if err := config.Get("elastic").Scan(&conf.ElasticSearch); err != nil {
		panic(pf("elastic", err))
	}
	// 加载RabbitMq配置
	if err := config.Get("rabbitmq").Scan(&conf.RabbitMq); err != nil {
		panic(pf("rabbitmq", err))
	}
	// 加载Common配置
	if err := config.Get("common").Scan(&conf.Common); err != nil {
		panic(pf("common", err))
	}
	// 加载Socks配置
	if err := config.Get("shadowsocks").Scan(&conf.ShadowSocks); err != nil {
		panic(pf("shadowsocks", err))
	}
	// 加载日志配置
	if err := config.Get("logger").Scan(&conf.Logger); err != nil {
		panic(pf("logger", err))
	}
	// 加载fofa账号配置
	if err := config.Get("fofa_account_list").Scan(&conf.Fofa); err != nil {
		panic(pf("fofa_account_list", err))
	}
	// 加载qichacha账号配置
	if err := config.Get("qichacha").Scan(&conf.QiChaCha); err != nil {
		panic(pf("qichacha", err))
	}
	// 加载qichacha账号配置
	if err := config.Get("tianyancha").Scan(&conf.Tianyancha); err != nil {
		panic(pf("tianyancha", err))
	}
	// 加载chinaz
	if err := config.Get("chinaz").Scan(&conf.Chinaz); err != nil {
		panic(pf("chinaz", err))
	}
	// 加载microkernel
	if err := config.Get("microkernel").Scan(&conf.Microkernel); err != nil {
		panic(pf("microkernel", err))
	}
	// 加载hunter账号配置
	if err := config.Get("hunter").Scan(&conf.Hunter); err != nil {
		panic(pf("hunter", err))
	}
	// 加载quake账号配置
	if err := config.Get("quake").Scan(&conf.Quake); err != nil {
		panic(pf("quake", err))
	}
	// 更新默认配置
	RefCfgDefVal(conf)
	return conf
}

const MicroKernelDefaultAddr = "http://127.0.0.1:61234"

// RefCfgDefVal 系统默认配置
func RefCfgDefVal(confs ...*Cfg) {
	conf := singleInstanceConf
	if len(confs) != 0 {
		conf = confs[0]
	}
	// 线索默认缓存天数
	if conf.Common.ClueCacheDay == 0 {
		conf.Common.ClueCacheDay = 7
	}
	// 资产默认缓存天数
	if conf.Common.AssetCacheDay == 0 {
		conf.Common.AssetCacheDay = 7
	}
	// APP默认缓存天数
	if conf.Common.AppCacheDay == 0 {
		conf.Common.AppCacheDay = 7
	}
	// 数据泄露缓存天数
	if conf.Common.DataLeakCacheDay == 0 {
		conf.Common.DataLeakCacheDay = 7
	}
	// 公众号默认缓存天数
	if conf.Common.OfficialAccountCacheDay == 0 {
		conf.Common.OfficialAccountCacheDay = 7
	}
	// ICP默认缓存天数
	if conf.Common.IcpCacheDay == 0 {
		conf.Common.IcpCacheDay = 7
	}
	if conf.Common.Client.Platform == "" {
		conf.Common.Client.Platform = "saas"
	}
	if conf.Common.Client.ApiPath == "" {
		conf.Common.Client.ApiPath = "https://api.baimaohui.net"
	}
	if conf.Common.Email.MaxClient <= 0 {
		conf.Common.Email.MaxClient = 1
	}
	// 企查查默认缓存天数
	if conf.QiChaCha.BasicDetailCacheDay == 0 {
		conf.QiChaCha.BasicDetailCacheDay = 60
	}
	// 天眼查默认缓存天数
	if conf.Tianyancha.CacheDay == 0 {
		conf.Tianyancha.CacheDay = 60
	}
	// Whois默认缓存天数
	if conf.Common.WhoisCacheDay == 0 {
		conf.Common.WhoisCacheDay = 7
	}
	if conf.Microkernel.Addr == "" {
		conf.Microkernel.Addr = MicroKernelDefaultAddr
	}
	// 本地化环境性能指标
	if conf.Common.Local.CompanyCount == 0 {
		conf.Common.Local.CompanyCount = 200
	}
	if conf.Common.Local.OtherCompanyCount == 0 {
		conf.Common.Local.OtherCompanyCount = 100
	}
	if conf.Common.Local.OtherClueCount == 0 {
		conf.Common.Local.OtherClueCount = 100
	}
	// 默认GithubToken
	if conf.Common.GitHubTokens == nil {
		conf.Common.GitHubTokens = make([]string, 1)
		conf.Common.GitHubTokens[0] = "****************************************"
		log.Println("【Notice】: Consul未获取到配置信息，使用默认配置，请及时检测，【github】 token is empty, use default token!")
	}
	// 默认GiteeToken
	if conf.Common.GiteeTokens == nil {
		conf.Common.GiteeTokens = make([]string, 1)
		conf.Common.GiteeTokens[0] = "0d5f9db582f6d31ae7dbea6a2f7f9b39"
		log.Println("【Notice】: Consul未获取到配置信息，使用默认配置，请及时检测，【gitee】 token is empty, use default token!")
	}
	// 默认GitcodeToken
	if conf.Common.GitcodeTokens == nil {
		conf.Common.GitcodeTokens = make([]string, 1)
		conf.Common.GitcodeTokens[0] = "R-88qbojotbV-RfbGNwBpXj2"
		log.Println("【Notice】: Consul未获取到配置信息，使用默认配置，请及时检测，【gitcode】 token is empty, use default token!")
	}
	if len(conf.Fofa) == 0 {
		// 如果fofa账号配置为空，则使用默认配置
		conf.Fofa = []Fofa{
			{
				FofaEmail:   "<EMAIL>",
				FofaKey:     "bf96e714021457f6808b2aed62a208aa",
				FofaSize:    200,
				FofaPullMax: 10000,
				FofaTimeOut: 30,
			},
		}
		log.Println("【Notice】: Consul未获取到配置信息，使用默认配置，请及时检测，【fofa】account config is empty, use default config!")
	}
	if conf.Redis.AsyncQueueSize == 0 {
		conf.Redis.AsyncQueueSize = 40
	}
	if conf.APP.CrawlerNum == 0 {
		conf.APP.CrawlerNum = 200
	}
	// RabbitMQ 默认配置
	if conf.RabbitMq.PrefetchCount == 0 {
		conf.RabbitMq.PrefetchCount = 10 // 提高预取数量，减少连接阻塞
	}
	if conf.RabbitMq.MaxConnections == 0 {
		conf.RabbitMq.MaxConnections = 50 // 最大连接数限制
	}
	if conf.RabbitMq.ConnectionPool == 0 {
		conf.RabbitMq.ConnectionPool = 5 // 连接池大小
	}
	if conf.Common.Client.Platform == "" {
		conf.Common.Client.Platform = "local"
	}
}
