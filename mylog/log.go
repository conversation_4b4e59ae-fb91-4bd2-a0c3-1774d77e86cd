package mylog

import (
	"log"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

type LogConf struct {
	Output    string
	ErrOutput string `toml:"err_output"`
}

var Logger *zap.Logger

func SetupLogger(conf LogConf) {
	log.SetFlags(log.Ldate | log.Ltime | log.Lmicroseconds | log.Lshortfile)

	cfg := zap.NewProductionConfig()
	cfg.OutputPaths = []string{conf.Output}
	cfg.ErrorOutputPaths = []string{conf.ErrOutput}
	cfg.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	cfg.EncoderConfig.StacktraceKey = ""

	var err error
	Logger, err = cfg.Build()
	if err != nil {
		log.Fatal(err)
	}

	log.Printf("config:%+v\n", conf)
}
