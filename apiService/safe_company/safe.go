package safe_company

import (
	"errors"
	"micro-service/apiService/wrapper"
	"micro-service/initialize/mysql"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/safe_user_company"
	"micro-service/middleware/mysql/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type SafeUserCompany struct {
	OptCompanyID int64 `json:"operate_company_id" form:"operate_company_id"`
}

// GetSafeCompanyUser return operatorId, operateUserId, companyId, err
//
// 仅适用于通过web login接口登陆后获取信息
func GetSafeCompanyUser(ctx *gin.Context, ocId ...int64) (uint64, uint64, int64, error) {
	// 根据token获取当前登陆用户ID
	userID := wrapper.UserIDWithGinCtx(ctx)
	var companyId int64
	if len(ocId) > 0 {
		companyId = ocId[0]
	} else {
		companyId = wrapper.OperateCompanyId(ctx)
	}

	return getInfo(userID, companyId)
}

func getInfo(userID uint64, optCompanyID int64) (operatorId, companyUserId uint64, companyId int64, err error) {
	// 获取用户信息
	info, err := user.NewUserModel(mysql.GetInstance()).FindById(userID)
	if err != nil {
		return 0, 0, 0, err
	}

	// 销售用户操作
	if info.Role == user.UserRoleSales {
		return userID, userID, 0, nil
	}

	isAdmin := info.Role == user.UserRoleAdmin || info.Role == user.UserRoleSafe
	companyClient := company.NewCompanyModel()

	// 安服|管理员操作自己
	if isAdmin && optCompanyID <= 0 {
		// 安服用户操作自己时，需要返回自己的企业ID
		if info.Role == user.UserRoleSafe {
			// 获取安服用户自己的企业信息
			if info.CompanyId != nil && info.CompanyId.Int64 > 0 {
				return userID, userID, info.CompanyId.Int64, nil
			}
		}
		// 管理员或者安服用户没有企业ID时返回0
		return userID, userID, 0, nil
	}

	// 安服|管理员操作普通用户
	if isAdmin && optCompanyID > 0 {
		err = safe_user_company.NewModel(mysql.GetInstance()).Has(userID, uint64(optCompanyID))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return 0, 0, 0, errors.New("用户无操作权限")
			}
			return 0, 0, 0, err
		}
		companyInfo, findErr := companyClient.FindById(uint64(optCompanyID), 0)
		if findErr != nil {
			if errors.Is(findErr, gorm.ErrRecordNotFound) {
				return 0, 0, 0, errors.New("企业信息不存在")
			}
			return 0, 0, 0, findErr
		}
		return userID, companyInfo.OwnerId, optCompanyID, nil
	}

	// 普通用户操作自己
	companyInfo, err := companyClient.FindById(0, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, 0, 0, errors.New("用户企业信息不存在")
		}
		return 0, 0, 0, err
	}
	return userID, userID, int64(companyInfo.ID), nil
}

func IsAdmin(c *gin.Context) (bool, error) {
	userID := wrapper.UserIDWithGinCtx(c)
	// 获取用户信息
	info, err := user.NewUserModel(mysql.GetInstance()).FindById(userID)
	if err != nil {
		return false, err
	}

	if info.Role == user.UserRoleAdmin {
		return true, nil
	}
	return false, nil
}

func ClientId(ctx *gin.Context) string {
	return wrapper.ClientId(ctx)
}

func IsAdminUser(ctx *gin.Context) bool {
	value, exist := ctx.Get(wrapper.GinUserInfo)
	if !exist || value == nil {
		return false
	}

	info, ok := value.(*user.User)
	if !ok || info == nil {
		return false
	}
	return info.Role == user.UserRoleAdmin
}

func IsSafeUser(ctx *gin.Context) bool {
	value, exist := ctx.Get(wrapper.GinUserInfo)
	if !exist || value == nil {
		return false
	}

	info, ok := value.(*user.User)
	if !ok || info == nil {
		return false
	}
	return info.Role == user.UserRoleSafe
}
