package router

import (
	"github.com/gin-gonic/gin"

	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"
)

// 单位资产测绘/云端资产推荐

func detectAssetTaskRouterRegister(r *gin.RouterGroup) {
	const scope = scopeTenantOrSafe + "|" + scopeAdmin
	// 下发测绘任务
	r.POST("/task/create", wrapper.Auth(api.DetectAssetTaskCreate, scope))
	// 线索更新
	r.PUT("/clues/update_sync", wrapper.Auth(api.DetectAssetTaskCluesUpdate, scopeSafe))

	// 任务简报
	report := r.Group("/report")
	report.GET("/info", wrapper.Auth(api.DetectAssetTaskReportInfo)) // 简报信息 scope
	report.GET("/import_asset_download", wrapper.Auth(api.DetectAssetTaskReportImportAssetDownload))
	report.GET("/clue_list", wrapper.Auth(api.DetectAssetTaskReportClueList)) // 线索列表

	// 资产测绘-企业查询
	company := r.Group("/company")
	// 资产测绘-获取公司下拉列表
	company.GET("drop_list", wrapper.Auth(api.DetectAssetCompanyDropList, scopeAll))

	// 资产测绘-结果相关
	result := r.Group("/result")
	// 资产测绘-资产评估
	result.POST("/assets/evaluate", wrapper.Auth(api.AssetsEvaluate, scopeAll))
	// ScanRecommend
	result.POST("/scan", wrapper.Auth(api.ScanRecommend, scopeAll))

	// 资产测绘关联异步任务
	r.POST("/relate", wrapper.Auth(api.TaskRelate, scope))
}
