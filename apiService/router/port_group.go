package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 端口分组路由
func portGroupRouterRegister(r *gin.RouterGroup) {
	// 端口列表-分页
	r.GET("/", wrapper.Auth(api.PortIndex, scopeAll))
	// 端口列表-不分页
	r.GET("/list", wrapper.Auth(api.PortList, scopeAll))
	// 端口添加
	r.POST("/", wrapper.Auth(api.PortAdd, scopeAll))
	// 端口编辑
	r.PUT("/:id", wrapper.Auth(api.PortEdit, scopeAll))
	// 端口删除
	r.DELETE("/", wrapper.Auth(api.PortDel, scopeAll))
	// 端口更新状态
	r.PUT("/update/status", wrapper.Auth(api.PortUpdateStatus, scopeAll))
	// 端口协议列表
	r.GET("/protocols", wrapper.Auth(api.PortProtocolIndex, scopeAll))
	// 端口详情
	r.POST("/detail", wrapper.Auth(api.PortDetail, scopeAll))

	// 端口分组列表-分页
	r.GET("/groups", wrapper.Auth(api.PortGroupIndex, scopeAll))
	// 端口分组添加
	r.POST("/groups", wrapper.Auth(api.PortGroupAdd, scopeAll))
	// 端口分组删除
	r.DELETE("/groups", wrapper.Auth(api.PortGroupDel, scopeAll))
	// 端口分组列表-不分页
	r.GET("/groups/list", wrapper.Auth(api.GetPortGroupList, scopeAll))
	// 端口分组编辑
	r.PUT("/groups/:id", wrapper.Auth(api.PortGroupEdit, scopeAll))
	// 端口分组详情
	r.POST("/groups/detail", wrapper.Auth(api.PortGroupDetail, scopeAll))
}
