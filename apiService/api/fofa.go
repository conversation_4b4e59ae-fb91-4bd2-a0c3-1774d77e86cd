package api

import (
	"errors"
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	scan "micro-service/scanService/proto"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func Query(ctx *gin.Context) error {
	// 记录请求开始
	startTime := time.Now()
	clientId := safe_company.ClientId(ctx)
	log.WithContextInfof(ctx, "[FOFA_QUERY_START] 开始处理FOFA查询请求, client_id: %s, 开始时间: %s",
		clientId, startTime.Format("2006-01-02 15:04:05.000"))

	if err := ClearCoreService(ctx); err != nil {
		log.WithContextErrorf(ctx, "[FOFA_QUERY_ERROR] ClearCoreService失败, client_id: %s, 错误: %v", clientId, err)
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}

	var rsp *pb.FofaQueryResponse
	param := pb.FofaQueryRequest{}
	err := ctx.BindJSON(&param)
	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_QUERY_ERROR] 参数绑定失败, client_id: %s, 错误: %v", clientId, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 记录请求参数
	log.WithContextInfof(ctx, "[FOFA_QUERY_PARAMS] 请求参数, client_id: %s, 参数: %s", clientId, utils.AnyToStr(param))

	// 记录开始调用FOFA服务
	fofaCallStart := time.Now()
	log.WithContextInfof(ctx, "[FOFA_QUERY_CALL_START] 开始调用FOFA服务, client_id: %s, 调用时间: %s",
		clientId, fofaCallStart.Format("2006-01-02 15:04:05.000"))

	rsp, err = pb.GetProtoCoreClient().FofaQuery(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(120))

	// 记录FOFA服务调用结果
	fofaCallEnd := time.Now()
	fofaCallDuration := fofaCallEnd.Sub(fofaCallStart)

	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_QUERY_CALL_ERROR] FOFA服务调用失败, client_id: %s, 调用耗时: %v, 错误详情: %v",
			clientId, fofaCallDuration, err)

		// 检查是否是429错误
		if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
			log.WithContextWarnf(ctx, "[FOFA_QUERY_RATE_LIMIT] FOFA接口限流, client_id: %s, 错误: %v", clientId, err)
		}

		// 检查是否是超时错误
		if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "deadline") {
			log.WithContextWarnf(ctx, "[FOFA_QUERY_TIMEOUT] FOFA接口超时, client_id: %s, 错误: %v", clientId, err)
		}

		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	log.WithContextInfof(ctx, "[FOFA_QUERY_CALL_SUCCESS] FOFA服务调用成功, client_id: %s, 调用耗时: %v, 返回数据条数: %d",
		clientId, fofaCallDuration, len(rsp.GetSdata()))

	// 记录总耗时
	totalDuration := time.Since(startTime)
	log.WithContextInfof(ctx, "[FOFA_QUERY_SUCCESS] FOFA查询请求处理完成, client_id: %s, 总耗时: %v, 请求参数: %s",
		clientId, totalDuration, utils.AnyToStr(param))

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func QueryCount(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaQueryCountResponse
	param := pb.FofaQueryCountRequest{}
	err := ctx.BindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	log.WithContextInfof(ctx, "Received Api.FofaQueryCount client_id: %s , request: %s", safe_company.ClientId(ctx), utils.AnyToStr(param))
	rsp, err = pb.GetProtoCoreClient().FofaQueryCount(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func QueryHost(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaHostResponse
	param := pb.FofaHostRequest{}
	err := ctx.BindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	log.WithContextInfof(ctx, "Received Api.FofaQueryHost client_id: %s , request: %s", safe_company.ClientId(ctx), utils.AnyToStr(param))
	rsp, err = pb.GetProtoCoreClient().FofaHost(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(120))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func QueryAllAssetsCount(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	modeStr := ctx.Query("query_mode")
	queryMode, _ := strconv.Atoi(modeStr)
	param := &pb.FofaAllAssetsCountRequest{QueryMode: int64(queryMode)}
	_ = ctx.ShouldBindJSON(&param)

	rsp, err := pb.GetProtoCoreClient().FofaAllAssetsCount(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		log.WithContextErrorf(ctx, "request /fofa/all_count api failed, err: %+v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, errors.New("获取数据失败"))
	}
	log.WithContextInfof(ctx, "Received Api.FofaQueryAllAssetsCount client_id: %s , request: %s", safe_company.ClientId(ctx), utils.AnyToStr(param))
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func FofaHot(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaQueryHotResponse
	rsp, err := pb.GetProtoCoreClient().FofaQueryHot(middleware.ContextWithSpan(ctx), &pb.FofaQueryHotRequest{Count: ctx.Param("count")}, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func QueryPureDns(ctx *gin.Context) error {
	// 记录请求开始
	startTime := time.Now()
	clientId := safe_company.ClientId(ctx)
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_START] 开始处理PureDns请求, client_id: %s, 开始时间: %s",
		clientId, startTime.Format("2006-01-02 15:04:05.000"))

	if err := ClearCoreService(ctx); err != nil {
		log.WithContextErrorf(ctx, "[FOFA_PUREDNS_ERROR] ClearCoreService失败, client_id: %s, 错误: %v", clientId, err)
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}

	var rsp *pb.FofaPureDnsResponse
	param := pb.FofaPureDnsRequest{}
	err := ctx.ShouldBindQuery(&param)
	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_PUREDNS_ERROR] 参数绑定失败, client_id: %s, 错误: %v", clientId, err)
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 记录请求参数
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_PARAMS] 请求参数, client_id: %s, 参数: %s", clientId, utils.AnyToStr(param))

	// 记录开始调用FOFA服务
	fofaCallStart := time.Now()
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_CALL_START] 开始调用FOFA服务, client_id: %s, 调用时间: %s",
		clientId, fofaCallStart.Format("2006-01-02 15:04:05.000"))

	rsp, err = pb.GetProtoCoreClient().FofaPureDns(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(120))

	// 记录FOFA服务调用结果
	fofaCallEnd := time.Now()
	fofaCallDuration := fofaCallEnd.Sub(fofaCallStart)

	if err != nil {
		log.WithContextErrorf(ctx, "[FOFA_PUREDNS_CALL_ERROR] FOFA服务调用失败, client_id: %s, 调用耗时: %v, 错误详情: %v",
			clientId, fofaCallDuration, err)

		// 检查是否是429错误
		if strings.Contains(err.Error(), "429") || strings.Contains(err.Error(), "Too Many Requests") {
			log.WithContextWarnf(ctx, "[FOFA_PUREDNS_RATE_LIMIT] FOFA接口限流, client_id: %s, 错误: %v", clientId, err)
		}

		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	log.WithContextInfof(ctx, "[FOFA_PUREDNS_CALL_SUCCESS] FOFA服务调用成功, client_id: %s, 调用耗时: %v, 返回数据条数: %d",
		clientId, fofaCallDuration, len(rsp.GetItems()))

	// 记录总耗时
	totalDuration := time.Since(startTime)
	log.WithContextInfof(ctx, "[FOFA_PUREDNS_SUCCESS] PureDns请求处理完成, client_id: %s, 总耗时: %v, 请求参数: %s",
		clientId, totalDuration, utils.AnyToStr(param))

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func FOFAQueryParse(ctx *gin.Context) error {
	var req scan.FofaParseQueryRequest
	_ = ctx.ShouldBindQuery(&req)
	if req.Page == 0 || req.PerPage == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "分页参数必须大于0")
	}
	req.PerPage = utils.If(req.PerPage > 50, 50, req.PerPage)

	if !safe_company.IsAdminUser(ctx) {
		_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
		if err != nil {
			return response.Gen(ctx).SendByError(api.ServiceName, err)
		}
		req.UserId = []uint64{userId}
	}

	rsp, err := scan.GetProtoClient().
		FofaParseQuery(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(scan.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(scan.ServiceName, response.MsgSuccess, rsp)
}

func FOFAParseStatistics(ctx *gin.Context) error {
	var req scan.FofaParseQueryRequest
	_ = ctx.ShouldBindQuery(&req)

	if !safe_company.IsAdminUser(ctx) {
		_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
		if err != nil {
			return response.Gen(ctx).SendByError(api.ServiceName, err)
		}
		req.UserId = []uint64{userId}
	}

	rsp, err := scan.GetProtoClient().
		FofaParseStatistics(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(scan.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(scan.ServiceName, response.MsgSuccess, rsp)
}

func FofaAccountInfo(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaAccountResponse
	rsp, err := pb.GetProtoCoreClient().FofaAccountInfo(middleware.ContextWithSpan(ctx), nil, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// FofaCreateScanTask 创建扫描任务
func FofaCreateScanTask(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaDetectionResponse
	param := pb.FofaScanTaskRequest{}
	paramsErr := ctx.BindJSON(&param)
	if paramsErr != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoCoreClient().CreateScanTask(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Data.Id)
}

// FofaCreateDetectionTask 创建探活任务
func FofaCreateDetectionTask(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaDetectionResponse
	param := pb.FofaDetectionRequest{}
	paramsErr := ctx.BindJSON(&param)
	if paramsErr != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoCoreClient().CreateDetectionTask(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Data.Id)
}

// FofaGetTaskStatus 获取任务状态
func FofaGetTaskStatus(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaTaskStatusResponse
	param := pb.FofaTaskStatusRequest{Id: ctx.Param("id")}
	if paramsErr := ctx.ShouldBindQuery(&param); paramsErr != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoCoreClient().GetTaskStatus(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Data)
}

// FofaGetTaskResult 获取任务结果
func FofaGetTaskResult(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaTaskResultResponse
	param := pb.FofaTaskStatusRequest{Id: ctx.Param("id")}
	if paramsErr := ctx.ShouldBindQuery(&param); paramsErr != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoCoreClient().GetTaskResult(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(3600))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Result)
}

// FofaDomainUpdateTask-批量下发给fofa去更新相关的域名数据的扫描任务
func FofaDomainUpdateTask(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var rsp *pb.FofaDomainTaskResponse
	param := pb.FofaDomainTaskRequest{}
	paramsErr := ctx.BindJSON(&param)
	if paramsErr != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, paramsErr)
	}
	rsp, err := pb.GetProtoCoreClient().CreateDomainUpdateTask(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp.Message)
}
