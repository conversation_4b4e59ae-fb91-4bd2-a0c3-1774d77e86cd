package api

import (
	"context"
	"errors"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/log"
	"micro-service/pkg/validate"
	pb "micro-service/webService/proto"

	"github.com/gin-gonic/gin"
)

func HasLoggedIn(ctx *gin.Context) error {
	userID, _, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	resp, err := pb.GetProtoClient().
		HasLoggedIn(context.Background(),
			&pb.HasLoggedInRequest{UserId: userID})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}

	has := map[string]bool{
		"has": resp.Has,
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", has)
}

type PublicNoticeAddParam struct {
	Notice    string `json:"notice" validate:"required" zh:"公告内容"`
	UpAtStart string `json:"up_at_start" validate:"required" zh:"升级开始时间"`
	UpAtEnd   string `json:"up_at_end" validate:"required" zh:"升级结束时间"`
}

func PublicNoticeAdd(ctx *gin.Context) error {
	isAdmin, err := safe_company.IsAdmin(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if !isAdmin {
		return response.Gen(ctx).SendByError(api.ServiceName, errors.New("该用户无权限操作此接口"))
	}

	var param PublicNoticeAddParam
	err = ctx.ShouldBindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	resp, err := pb.GetProtoClient().
		PublicNoticeAdd(context.Background(),
			&pb.PublicNoticeAddRequest{Notice: param.Notice, UpAtStart: param.UpAtStart, UpAtEnd: param.UpAtEnd})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", resp)
}

type PublicNoticeDelParam struct {
	ID uint64 `json:"id" validate:"required" zh:"公告ID"`
}

func PublicNoticeDel(ctx *gin.Context) error {
	isAdmin, err := safe_company.IsAdmin(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if !isAdmin {
		return response.Gen(ctx).SendByError(api.ServiceName, errors.New("该用户无权限操作此接口"))
	}

	var param PublicNoticeDelParam
	err = ctx.ShouldBindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, err = pb.GetProtoClient().
		PublicNoticeDel(context.Background(),
			&pb.PublicNoticeDelRequest{Id: param.ID})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
}

type PublicNoticeSaveParam struct {
	PublicNoticeAddParam
	ID uint64 `json:"id" validate:"required" zh:"公告ID"`
}

func PublicNoticeSave(ctx *gin.Context) error {
	isAdmin, err := safe_company.IsAdmin(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if !isAdmin {
		return response.Gen(ctx).SendByError(api.ServiceName, errors.New("该用户无权限操作此接口"))
	}

	var param PublicNoticeSaveParam
	err = ctx.ShouldBindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, err = pb.GetProtoClient().
		PublicNoticeSave(context.Background(),
			&pb.PublicNoticeSaveRequest{Id: param.ID, Notice: param.Notice, UpAtStart: param.UpAtStart, UpAtEnd: param.UpAtEnd})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
}

type PublicNoticeListParam struct {
	CurrentPage int64 `form:"current_page" validate:"required,gt=0" zh:"页码"`
	PerPage     int64 `form:"per_page" validate:"required,gt=0" zh:"页大小"`
}

func PublicNoticeList(ctx *gin.Context) error {
	log.Info("PublicNoticeList api enter")

	isAdmin, err := safe_company.IsAdmin(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if !isAdmin {
		return response.Gen(ctx).SendByError(api.ServiceName, errors.New("该用户无权限操作此接口"))
	}

	var param PublicNoticeListParam
	err = ctx.ShouldBindQuery(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	r, err := pb.GetProtoClient().
		PublicNoticeList(context.Background(),
			&pb.PublicNoticeListRequest{CurrentPage: param.CurrentPage, PerPage: param.PerPage})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", r)
}

func FindLatestNotice(ctx *gin.Context) error {
	r, err := pb.GetProtoClient().
		FindLatestNotice(context.Background(),
			&pb.Empty{})
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", r)
}
