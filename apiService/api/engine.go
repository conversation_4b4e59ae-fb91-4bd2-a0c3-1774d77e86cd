package api

import (
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	corePb "micro-service/coreService/proto"
	"micro-service/pkg/utils"

	"github.com/gin-gonic/gin"
)

func EngineWebSite(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	rsp, err := corePb.GetProtoCoreClient().EngineWebSite(middleware.ContextWithSpan(ctx), &corePb.EngineSearchRequest{
		Domain: ctx.Param("domain"),
	}, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(corePb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(corePb.ServiceName, "Success", rsp)
}
