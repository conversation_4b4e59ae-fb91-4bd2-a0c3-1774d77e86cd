package api

import (
	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/pkg/utils"
	pb "micro-service/scanService/proto"

	"github.com/gin-gonic/gin"
)

func BurstSubdomain(ctx *gin.Context) error {
	var rsp *pb.SubdomainResponse
	param := pb.ParamsRequest{}
	err := ctx.ShouldBindJSON(&param)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	rsp, err = pb.GetProtoClient().DomainBurst(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(300))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

func BurstSubdomainTaskDelete(ctx *gin.Context) error {
	var param = &pb.SubdomainResponse{}
	_ = ctx.ShouldBindJSON(param)
	if param.GolangTaskId == 0 {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "任务ID不可为空")
	}

	_, err := pb.GetProtoClient().DomainBurstTaskDelete(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", nil)
}
