package api

import (
	"micro-service/pkg/microx"
	"strconv"
	"time"

	"github.com/xuri/excelize/v2"

	"github.com/gin-gonic/gin"

	"fmt"
	"micro-service/apiService/middleware"
	mw "micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/ginx"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	pb "micro-service/webService/proto"

	"github.com/spf13/cast"
)

// convertToInt32 将any类型安全转换为int32
func convertToInt32(value any) int32 {
	if value == nil {
		return -999 // 特殊值，表示nil
	}

	switch v := value.(type) {
	case int:
		return int32(v)
	case int32:
		return v
	case int64:
		return int32(v)
	case float64:
		return int32(v)
	case string:
		if v == "" {
			return -999 // 特殊值，表示空字符串
		}
		if val, err := strconv.ParseInt(v, 10, 32); err == nil {
			return int32(val)
		}
		return -999 // 特殊值，表示无效字符串
	default:
		return -999 // 特殊值，表示未知类型
	}
}

// convertToInt64 将any类型安全转换为int64
func convertToInt64(value any) int64 {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case int32:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		if v == "" {
			return 0
		}
		if result, err := strconv.ParseInt(v, 10, 64); err == nil {
			return result
		}
		return 0
	default:
		return cast.ToInt64(value)
	}
}

// isEmptyValue 检查值是否为空（nil或空字符串）
func isEmptyValue(value any) bool {
	if value == nil {
		return true
	}
	if str, ok := value.(string); ok && str == "" {
		return true
	}
	return false
}

// convertToString 将any类型安全转换为字符串
func convertToString(value any) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int64:
		return strconv.FormatInt(v, 10)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	default:
		return fmt.Sprintf("%v", v)
	}
}

func DomainAssetsCronInfo(ctx *gin.Context) error {
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param := &pb.DomainAssetsUpdateByCronRequest{UserId: userId}
	rsp, err := pb.GetProtoClient().DomainAssetCronInfo(mw.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func DomainAssetsUpdateByCron(ctx *gin.Context) error {
	var param pb.DomainAssetsUpdateByCronRequest
	_ = ctx.ShouldBindJSON(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	opId, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	param.CallByCron = false
	param.OperateUserId = int64(opId)
	_, err = pb.GetProtoClient().
		DomainAssetUpdateByCron(ctx, &param, utils.RpcTimeoutDur(time.Minute), microx.ServerTimeoutDur(6*time.Hour))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, response.MsgSuccess, nil)
}

func DomainAssetsDomainFilter(ctx *gin.Context) error {
	var param pb.DomainAssetsDomainFilterRequest
	_ = ctx.ShouldBindQuery(&param)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	rsp, err := pb.GetProtoClient().DomainAssetDomainFilter(mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func DomainAssetsUpload(ctx *gin.Context) error {
	header, err := ctx.FormFile("file")
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	file, err := header.Open()
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	xls, err := excelize.OpenReader(file)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	rows, err := xls.GetRows("Sheet1")
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}
	// 去除表头
	rows = rows[1:]
	var data []string
	for _, r := range rows {
		if len(r) > 0 {
			data = append(data, r[0])
		}
	}
	return response.Gen(ctx).SendSuccess(api.ServiceName, response.MsgSuccess, data)
}

// DomainAssetsList 域名总资产列表
func DomainAssetsList(ctx *gin.Context) error {
	// 打印原始URL和查询参数
	fmt.Println("原始URL:", ctx.Request.URL.String())
	fmt.Println("原始查询参数:", ctx.Request.URL.Query())

	// 定义与proto一致的请求结构体
	type DomainAssetsListRequest struct {
		UserId           int64    `form:"user_id" json:"user_id"`                       // 用户ID
		OperateCompanyId string   `form:"operate_company_id" json:"operate_company_id"` // 操作企业ID（改为string类型）
		Keyword          string   `form:"keyword" json:"keyword"`                       // 关键字
		PerPage          int32    `form:"per_page" json:"per_page"`                     // 每页数量
		CompanyName      []string // 移除form标签，使用ginx.QueryArray处理
		Title            []string // 移除form标签，使用ginx.QueryArray处理
		Icp              []string // 移除form标签，使用ginx.QueryArray处理
		StatusCode       []string // 移除form标签，使用ginx.QueryArray处理
		CreatedAtRange   []string // 移除form标签，使用ginx.QueryArray处理
		UpdatedAtRange   []string // 移除form标签，使用ginx.QueryArray处理
		Status           int32    `form:"status" json:"status"` // 域名状态 0/1 已失效/可解析
		Source           []int32  // 移除form标签，使用ginx.QueryArray处理
		Domain           []string // 移除form标签，使用ginx.QueryArray处理
		TopDomain        []string // 移除form标签，使用ginx.QueryArray处理
		FDomain          []string // 移除form标签，使用ginx.QueryArray处理
		ShowCondition    string   `form:"show_condition" json:"show_condition"`         // 是否显示筛选条件 1/不显示 0/显示
		WebsiteMessageId int64    `form:"website_message_id" json:"website_message_id"` // 网站信息ID
		Type             int32    `form:"type" json:"type"`                             // 域名类型 0/1 子域名/主域名
		OpenParse        int32    `form:"open_parse" json:"open_parse"`                 // 是否是泛解析  0/不是   1/是
		Page             int32    `form:"page" json:"page"`                             // 页码
	}

	// 绑定基础请求参数（非数组参数）
	var apiParam DomainAssetsListRequest
	if err := ctx.ShouldBindQuery(&apiParam); err != nil {
		fmt.Println("绑定API参数出错:", err)
	}

	// 使用ginx.QueryArray处理所有数组参数，支持PHP风格的数组格式
	apiParam.CompanyName = ginx.QueryArray(ctx, "company_name", true)
	apiParam.Title = ginx.QueryArray(ctx, "title", true)
	apiParam.Icp = ginx.QueryArray(ctx, "icp", true)
	apiParam.StatusCode = ginx.QueryArray(ctx, "status_code", true)
	apiParam.CreatedAtRange = ginx.QueryArray(ctx, "created_at_range", true)
	apiParam.UpdatedAtRange = ginx.QueryArray(ctx, "updated_at_range", true)
	apiParam.Domain = ginx.QueryArray(ctx, "domain", true)
	apiParam.TopDomain = ginx.QueryArray(ctx, "top_domain", true)
	apiParam.FDomain = ginx.QueryArray(ctx, "f_domain", true)

	// 处理Source数组参数（需要转换为int32）
	sourceStrings := ginx.QueryArray(ctx, "source", true)
	if len(sourceStrings) > 0 {
		apiParam.Source = make([]int32, 0, len(sourceStrings))
		for _, sourceStr := range sourceStrings {
			if sourceVal, err := strconv.Atoi(sourceStr); err == nil {
				apiParam.Source = append(apiParam.Source, int32(sourceVal))
			}
		}
	}

	// 打印绑定后的参数值
	typeForPrint := convertToInt32(apiParam.Type)
	if isEmptyValue(apiParam.Type) {
		typeForPrint = -1
	}
	fmt.Printf("API绑定参数 - Keyword: '%s', Type: %d, OpenParse: %d, Page: %d, PerPage: %d\n",
		apiParam.Keyword, typeForPrint, apiParam.OpenParse, apiParam.Page, apiParam.PerPage)

	// 打印数组参数的解析结果
	fmt.Printf("数组参数解析结果:\n")
	fmt.Printf("  CompanyName: %v\n", apiParam.CompanyName)
	fmt.Printf("  Title: %v\n", apiParam.Title)
	fmt.Printf("  Icp: %v\n", apiParam.Icp)
	fmt.Printf("  StatusCode: %v\n", apiParam.StatusCode)
	fmt.Printf("  Source: %v\n", apiParam.Source)
	fmt.Printf("  Domain: %v\n", apiParam.Domain)
	fmt.Printf("  TopDomain: %v\n", apiParam.TopDomain)
	fmt.Printf("  FDomain: %v\n", apiParam.FDomain)
	fmt.Printf("  CreatedAtRange: %v\n", apiParam.CreatedAtRange)
	fmt.Printf("  UpdatedAtRange: %v\n", apiParam.UpdatedAtRange)

	// 获取当前用户ID和企业ID
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 创建proto请求参数
	param := &pb.DomainAssetsListRequest{
		UserId:           int64(userId),
		OperateCompanyId: convertStringToInt64(apiParam.OperateCompanyId),
		Keyword:          apiParam.Keyword,
		PerPage:          apiParam.PerPage,
		CompanyName:      apiParam.CompanyName,
		Title:            apiParam.Title,
		Icp:              apiParam.Icp,
		StatusCode:       apiParam.StatusCode,
		CreatedAtRange:   apiParam.CreatedAtRange,
		UpdatedAtRange:   apiParam.UpdatedAtRange,
		Status:           apiParam.Status,
		Source:           apiParam.Source,
		Domain:           apiParam.Domain,
		TopDomain:        apiParam.TopDomain,
		FDomain:          apiParam.FDomain,
		ShowCondition:    apiParam.ShowCondition,
		WebsiteMessageId: apiParam.WebsiteMessageId,
		Type: func() int32 {
			if isEmptyValue(apiParam.Type) {
				return -1
			}
			typeValue := convertToInt32(apiParam.Type)
			if typeValue == -999 {
				return -1
			}
			return typeValue
		}(),
		OpenParse: apiParam.OpenParse,
		Page:      apiParam.Page,
	}

	// 设置默认每页数量
	if param.PerPage == 0 {
		param.PerPage = 10
	}

	// 打印传递给proto的关键参数
	fmt.Printf("传递给proto的参数 - Keyword: '%s', UserId: %d, Page: %d, PerPage: %d\n",
		param.Keyword, param.UserId, param.Page, param.PerPage)

	// 直接从URL中获取page参数，确保正确处理
	pageStr := ctx.Query("page")
	if pageStr != "" {
		if pageVal, err := strconv.Atoi(pageStr); err == nil && pageVal > 0 {
			param.Page = int32(pageVal)
			fmt.Printf("从URL直接获取页码: %d\n", param.Page)
		} else {
			param.Page = 1
			fmt.Println("无效的page参数，设置默认页码为1")
		}
	} else {
		param.Page = 1
		fmt.Println("设置默认页码为1")
	}

	// 检查URL查询参数，获取原始值（未经过类型转换）
	queryValues := ctx.Request.URL.Query()
	typeValue := queryValues.Get("type")
	openParseValue := queryValues.Get("open_parse")
	status := queryValues.Get("status")
	fmt.Printf("原始查询字符串 - type: '%s', open_parse: '%s'\n", typeValue, openParseValue)

	// 处理type参数 - 如果参数不存在或为空字符串，则视为未指定
	if typeValue == "" {
		fmt.Println("请求中未指定或为空的type参数，设置为-1")
		param.Type = -1
	} else {
		fmt.Printf("请求中指定了非空type参数: '%s'\n", typeValue)
	}

	// 处理open_parse参数 - 如果参数不存在或为空字符串，则视为未指定
	if openParseValue == "" {
		fmt.Println("请求中未指定或为空的open_parse参数，设置为-1")
		param.OpenParse = -1
	} else {
		fmt.Printf("请求中指定了非空open_parse参数: '%s'\n", openParseValue)
		if openParseInt, err := strconv.Atoi(openParseValue); err == nil {
			param.OpenParse = int32(openParseInt)
			fmt.Printf("成功转换open_parse参数: %d\n", param.OpenParse)
		} else {
			fmt.Printf("open_parse参数转换失败: %v，设置为-1\n", err)
			param.OpenParse = -1
		}
	}

	// 处理status参数 - 如果参数不存在或为空字符串，则视为未指定
	if status == "" {
		fmt.Println("请求中未指定或为空的status参数，设置为-1")
		param.Status = -1
	} else {
		fmt.Printf("请求中指定了非空status参数: '%s'\n", status)
		if statusInt, err := strconv.Atoi(status); err == nil {
			param.Status = int32(statusInt)
			fmt.Printf("成功转换status参数: %d\n", param.Status)
		} else {
			fmt.Printf("status参数转换失败: %v，设置为-1\n", err)
			param.Status = -1
		}
	}
	// 打印最终构建的请求参数
	fmt.Printf("最终请求参数: UserId=%d, Type=%d, OpenParse=%d, Page=%d, PerPage=%d\n",
		param.UserId, param.Type, param.OpenParse, param.Page, param.PerPage)

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().DomainAssetsList(mw.ContextWithSpan(ctx), param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		fmt.Printf("RPC调用失败: %v\n", err)
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}

	// 打印响应数据的分页信息
	fmt.Printf("响应数据: Total=%d, CurrentPage=%d, PerPage=%d, LastPage=%d, Items=%d\n",
		rsp.Total, rsp.CurrentPage, rsp.PerPage, rsp.LastPage, len(rsp.Items))

	// 转换响应数据格式以匹配PHP格式
	responseData := convertDomainAssetsResponse(rsp)

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, responseData)
}

// convertDomainAssetsResponse 转换域名资产响应格式以匹配PHP格式
func convertDomainAssetsResponse(rsp *pb.DomainAssetsListResponse) map[string]interface{} {
	// 转换items数据
	items := make([]map[string]interface{}, 0, len(rsp.Items))
	for _, item := range rsp.Items {
		itemMap := map[string]interface{}{
			"id":             item.Id,
			"user_id":        item.UserId,
			"domain":         item.Domain,
			"company_name":   item.CompanyName,
			"f_domain":       item.FDomain,
			"top_domain":     item.TopDomain,
			"dns_a":          item.DnsA,
			"dns_aaaa":       item.DnsAaaa,
			"cname":          item.Cname,
			"source":         item.Source,
			"depth":          item.Depth,
			"status":         item.Status,
			"type":           item.Type,
			"created_at":     item.CreatedAt,
			"updated_at":     item.UpdatedAt,
			"open_parse":     item.OpenParse,
			"detect_task_id": item.DetectTaskId,
			"status_code":    item.StatusCode,
			"title":          item.Title,
			"icp":            item.Icp,
			"uniqud":         item.Uniqud,
			"has_next":       item.HasNext,
		}

		// 处理可能为空的字段，转换为null
		if item.CompanyId == 0 {
			itemMap["company_id"] = nil
		} else {
			itemMap["company_id"] = item.CompanyId
		}

		if item.OrganizationDiscoverTaskId == 0 {
			itemMap["organization_discover_task_id"] = nil
		} else {
			itemMap["organization_discover_task_id"] = item.OrganizationDiscoverTaskId
		}

		if item.WebsiteMessageId == 0 {
			itemMap["website_message_id"] = nil
		} else {
			itemMap["website_message_id"] = item.WebsiteMessageId
		}

		if item.OrganizationId == 0 {
			itemMap["organization_id"] = nil
		} else {
			itemMap["organization_id"] = item.OrganizationId
		}

		if item.CustomTags == "" {
			itemMap["custom_tags"] = nil
		} else {
			itemMap["custom_tags"] = item.CustomTags
		}

		if item.DetectTaskId == "" {
			itemMap["detect_task_id"] = nil
		} else {
			itemMap["detect_task_id"] = item.DetectTaskId
		}

		if item.Icp == "" {
			itemMap["icp"] = nil
		} else {
			itemMap["icp"] = item.Icp
		}

		// 添加到items数组
		items = append(items, itemMap)
	}

	// 转换condition数据
	var condition map[string]interface{}
	if rsp.Condition != nil {
		// 辅助函数：确保返回空数组而不是nil
		ensureArray := func(arr []string) []string {
			if arr == nil {
				return []string{}
			}
			return arr
		}

		condition = map[string]interface{}{
			"company_list": ensureArray(rsp.Condition.CompanyList),
			"domain":       ensureArray(rsp.Condition.Domain),
			"top_domain":   ensureArray(rsp.Condition.TopDomain),
			"f_domain":     ensureArray(rsp.Condition.FDomain),
			"title":        ensureArray(rsp.Condition.Title),
			"status_code":  ensureArray(rsp.Condition.StatusCode),
		}
	}

	return map[string]interface{}{
		"total":        rsp.Total,
		"per_page":     rsp.PerPage,
		"current_page": rsp.CurrentPage,
		"last_page":    rsp.LastPage,
		"from":         rsp.From,
		"to":           rsp.To,
		"items":        items,
		"condition":    condition,
	}
}

// DomainAssetsExport 域名总资产导出
func DomainAssetsExport(ctx *gin.Context) error {
	// 设置不限内存

	// 定义与proto一致的请求结构体
	type DomainAssetsExportRequest struct {
		OperateCompanyId any      `form:"operate_company_id" json:"operate_company_id"` // 操作企业ID
		Keyword          string   `form:"keyword" json:"keyword"`                       // 关键字
		CompanyName      []string `form:"company_name" json:"company_name"`             // 企业名称
		Title            []string `form:"title" json:"title"`                           // 标题
		Icp              []string `form:"icp" json:"icp"`                               // ICP备案
		StatusCode       []string `form:"status_code" json:"status_code"`               // 状态码
		CreatedAtRange   []string `form:"created_at_range" json:"created_at_range"`     // 创建时间范围
		UpdatedAtRange   []string `form:"updated_at_range" json:"updated_at_range"`     // 更新时间范围
		Status           any      `form:"status" json:"status"`                         // 域名状态 0/1 已失效/可解析
		Source           []int32  `form:"source" json:"source"`                         // 域名来源
		Domain           []string `form:"domain" json:"domain"`                         // 域名
		DomainArr        []string `form:"domain_arr" json:"domain_arr"`                 // 域名数组（替代domain）
		TopDomain        []string `form:"top_domain" json:"top_domain"`                 // 顶级域名
		FDomain          []string `form:"f_domain" json:"f_domain"`                     // 父级域名
		WebsiteMessageId string   `form:"website_message_id" json:"website_message_id"` // 网站信息ID
		Type             any      `form:"type" json:"type"`                             // 域名类型 0/1 子域名/主域名
		OpenParse        any      `form:"open_parse" json:"open_parse"`                 // 是否是泛解析  0/不是   1/是
		Menu             string   `form:"menu" json:"menu"`                             // 菜单名称
		Page             int32    `form:"page" json:"page"`                             // 页码
		PerPage          int32    `form:"per_page" json:"per_page"`                     // 每页数量
	}

	// 绑定请求参数
	var apiParam DomainAssetsExportRequest
	if err := ctx.ShouldBindJSON(&apiParam); err != nil {
		log.WithContextErrorf(ctx, "[域名导出] 参数绑定错误: %v", err)
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "参数错误!")
	}

	// 获取当前用户ID和企业ID
	opId, userId, companyId, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	// 记录日志
	log.WithContextInfof(ctx, "[域名导出] 用户ID=%d, 步骤=1, 时间=%d", userId, time.Now().Unix())

	// 创建proto请求参数
	param := &pb.DomainAssetsExportRequest{
		UserId:           int64(userId),
		OperateUserId:    int64(opId),
		CompanyId:        int64(companyId),
		OperateCompanyId: convertToInt64(apiParam.OperateCompanyId),
		Keyword:          apiParam.Keyword,
		CompanyName:      apiParam.CompanyName,
		Title:            apiParam.Title,
		Icp:              apiParam.Icp,
		StatusCode:       apiParam.StatusCode,
		CreatedAtRange:   apiParam.CreatedAtRange,
		UpdatedAtRange:   apiParam.UpdatedAtRange,
		Source:           apiParam.Source,
		Domain:           apiParam.Domain,
		DomainArr:        apiParam.DomainArr,
		TopDomain:        apiParam.TopDomain,
		FDomain:          apiParam.FDomain,
		Menu:             apiParam.Menu,
	}

	// 处理特殊参数 - Status
	if isEmptyValue(apiParam.Status) {
		param.Status = -1
	} else {
		statusValue := convertToInt32(apiParam.Status)
		if statusValue == -999 {
			// 转换失败，使用-1
			param.Status = -1
		} else {
			param.Status = statusValue
		}
	}

	// 处理特殊参数 - Type
	if isEmptyValue(apiParam.Type) {
		param.Type = -1
	} else {
		typeValue := convertToInt32(apiParam.Type)
		if typeValue == -999 {
			// 转换失败，使用-1
			param.Type = -1
		} else {
			param.Type = typeValue
		}
	}

	// 处理特殊参数 - OpenParse
	if isEmptyValue(apiParam.OpenParse) {
		param.OpenParse = -1
	} else {
		openParseValue := convertToInt32(apiParam.OpenParse)
		if openParseValue == -999 {
			// 转换失败，使用-1
			param.OpenParse = -1
		} else {
			param.OpenParse = openParseValue
		}
	}

	// 处理特殊参数 - WebsiteMessageId
	if apiParam.WebsiteMessageId == "" {
		param.WebsiteMessageId = 0
	} else {
		websiteMessageIdInt, err := strconv.ParseInt(apiParam.WebsiteMessageId, 10, 64)
		if err != nil {
			param.WebsiteMessageId = 0
		} else {
			param.WebsiteMessageId = websiteMessageIdInt
		}
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().DomainAssetsExport(mw.ContextWithSpan(ctx), param, utils.RpcTimeoutDur(time.Minute*10))
	if err != nil {
		log.WithContextErrorf(ctx, "[域名导出] 导出失败: %v", err)
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}

	return response.Gen(ctx).SendSuccess(api.ServiceName, response.MsgSuccess, rsp)
}

// DomainAssetsDelete 域名总资产列表删除数据
func DomainAssetsDelete(ctx *gin.Context) error {
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("domain_assets", "参数错误!")
	}

	// 验证请求参数
	var param struct {
		OperateCompanyID any      `json:"operate_company_id"`
		Keyword          string   `json:"keyword"`
		CompanyName      []string `json:"company_name"`
		Title            []string `json:"title"`
		Icp              []string `json:"icp"`
		WebsiteMessageId string   `json:"website_message_id"`
		StatusCode       []string `json:"status_code"`
		CreatedAtRange   []string `json:"created_at_range"`
		UpdatedAtRange   []string `json:"updated_at_range"`
		Status           any      `json:"status"`
		Source           []string `json:"source"`
		Domain           []string `json:"domain"`
		Type             any      `json:"type"`
		OpenParse        any      `json:"open_parse"`
		DomainArr        []string `json:"domain_arr"`
		TopDomain        []string `json:"top_domain"`
		FDomain          []string `json:"f_domain"`
	}

	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByErrorMsg("domain_assets", "参数错误!")
	}

	// 构建请求参数
	req := &pb.DomainAssetsDeleteRequest{
		UserId:           int64(userID),
		CompanyId:        int64(companyID),
		Keyword:          param.Keyword,
		CompanyName:      param.CompanyName,
		Title:            param.Title,
		Icp:              param.Icp,
		WebsiteMessageId: param.WebsiteMessageId,
		StatusCode:       param.StatusCode,
		CreatedAt:        param.CreatedAtRange,
		UpdatedAt:        param.UpdatedAtRange,
		Source:           param.Source,
		Domain:           param.Domain,
		DomainArr:        param.DomainArr,
		TopDomain:        param.TopDomain,
		FDomain:          param.FDomain,
	}

	// 处理特殊参数 - Status (转换为字符串)
	if isEmptyValue(param.Status) {
		req.Status = ""
	} else {
		req.Status = convertToString(param.Status)
	}

	// 处理特殊参数 - Type (转换为字符串)
	if isEmptyValue(param.Type) {
		req.Type = ""
	} else {
		req.Type = convertToString(param.Type)
	}

	// 处理特殊参数 - OpenParse (转换为字符串)
	if isEmptyValue(param.OpenParse) {
		req.OpenParse = ""
	} else {
		req.OpenParse = convertToString(param.OpenParse)
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().DomainAssetsDelete(middleware.ContextWithSpan(ctx), req)
	if err != nil {
		log.WithContextErrorf(ctx, "[域名资产] 删除域名资产数据失败: %v", err)
		return response.Gen(ctx).SendByError("domain_assets", err)
	}

	// 返回成功响应
	return response.Gen(ctx).SendSuccess("domain_assets", response.MsgSuccess, map[string]interface{}{
		"delete_count": rsp.DeleteCount,
	})
}

func FofaUpdate(ctx *gin.Context) error {
	var param pb.FofaUpdateRequest
	_ = ParseJSON(ctx, &param)

	// 校验企业租户是否有批量下发给fofa去更新相关的域名数据功能
	// 检查是否为客户端用户（企业租户）
	if !safe_company.IsAdminUser(ctx) && !safe_company.IsSafeUser(ctx) {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "您没有该功能的操作权限!")
	}

	// 获取用户ID和企业ID
	_, userId, _, err := safe_company.GetSafeCompanyUser(ctx, convertToInt64(param.OperateCompanyId))
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId

	// 验证请求参数
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().FofaUpdate(mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

func PushDomainAssets(ctx *gin.Context) error {
	var param pb.PushDomainDataRequest
	_ = ParseJSON(ctx, &param)

	// 获取用户ID和企业ID
	_, userId, companyId, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError(api.ServiceName, err)
	}

	param.UserId = userId
	param.CompanyId = uint64(companyId)
	// 验证请求参数
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, msg)
	}

	// 调用RPC服务
	rsp, err := pb.GetProtoClient().PushDomainData(mw.ContextWithSpan(ctx), &param, utils.RpcTimeoutDur(time.Minute))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}

	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, rsp)
}

// convertStringToInt64 将字符串转换为int64，处理特殊值
func convertStringToInt64(value string) int64 {
	if value == "" || value == "-1" {
		return -1
	}

	if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
		return intVal
	}

	return -1 // 转换失败时返回-1
}
