package api

import (
	"fmt"
	"micro-service/pkg/log"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"

	"micro-service/apiService/middleware"
	api "micro-service/apiService/proto"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	pb "micro-service/coreService/proto"
	"micro-service/pkg/utils"
	"micro-service/pkg/validate"
	web "micro-service/webService/proto"
	"strconv"
)

// ExpandByCompanyName 通过企业名称扩展线索
func ExpandByCompanyName(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	log.WithContextInfof(ctx, fmt.Sprintf("通过企业名称扩展线索-ExpandByCompanyName-api请求-,company_name:%s,force:%v,", param.Keyword, param.Force))
	// param
	var rsp *pb.ExpandClueResponse
	var err error
	for i := 0; i < 3; i++ {
		// 开始请求 接口
		rsp, err = pb.GetProtoCoreClient().ExpandCompanyName(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(45))
		if err == nil {
			break
		}
		log.WithContextWarnf(ctx, "通过企业名称扩展线索 - 重试 %d - 出现错误: %v", i+1, err)
		time.Sleep(time.Second)
	}
	if err != nil {
		log.WithContextErrorf(ctx, "通过企业名称扩展线索 - 扩展线索失败: %v", err)
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByIcp 通过ICP扩展线索
func ExpandByIcp(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandIcp(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// SearchDbClueByCompanyName 通过企业名称获取数据库线索
func SearchDbClueByCompanyName(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordSearchRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().SearchByCompanyName(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByIp 通过IP扩展线索
func ExpandByIp(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandIp(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByDomain 通过根域扩展线索
func ExpandByDomain(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandDomain(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandBySubDomain 通过子域扩展线索
func ExpandBySubDomain(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandSubDomain(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByCert 通过证书扩展线索
func ExpandByCert(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandCert(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByIcon 通过Icon扩展线索
func ExpandByIcon(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandIconRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandIcon(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandByKeyword 通过关键字扩展线索
func ExpandByKeyword(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandKeyword(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandResult 获取扩展结果
func ExpandResult(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandResultRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().GetExpandResult(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// GetClueList 获取线索列表
func GetClueList(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := &pb.ClueListRequest{}

	log.Infof("ctx body: %+v", ctx.Request.Body)
	log.Infof("param: %#v", param)
	_ = ctx.ShouldBindJSON(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().GetClueList(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// UpdateClueInfo 更新线索信息
func UpdateClueInfo(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ClueInfo{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	param.Id = cast.ToUint64(ctx.Param("id"))
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().UpdateClueInfo(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// CreateClue 创建线索
func CreateClue(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ClueInfo{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().CreateClue(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// UpdateByKeyword 更新线索信息
func UpdateByKeyword(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.UpdateClueByKeywordRequest{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().UpdateByKeyword(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// ExpandTaskList 线索扩展任务列表
func ExpandTaskList(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := pb.ExpandClueTaskListRequest{}
	if err := ctx.ShouldBindQuery(&param); err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	param.CreatedAt = utils.GetQueryTimeRange("created_at", ctx)
	param.UpdatedAt = utils.GetQueryTimeRange("updated_at", ctx)
	if ok, msg := validate.Validator(&param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	// param
	rsp, err := pb.GetProtoCoreClient().ExpandClueTaskList(middleware.ContextWithSpan(ctx), &param, utils.SetRpcTimeoutOpt(30))
	// 开始请求 接口
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	// 返回数据
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// CluesBatchUpdate 批量更新线索信息
func CluesBatchUpdate(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	param := &pb.ClueListRequest{}
	_ = ctx.ShouldBindJSON(param)

	if ok, msg := validate.Validator(param); !ok {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, msg)
	}
	if param.UpdateCompanyName == "" {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, "线索记录更新的企业名称不可为空")
	}
	if len(param.CreatedAt) == 2 {
		param.CreatedAtBefore = param.CreatedAt[0]
		param.CreatedAtAfter = param.CreatedAt[1]
	}
	if len(param.UpdatedAt) == 2 {
		param.UpdatedAtBefore = param.UpdatedAt[0]
		param.UpdatedAtAfter = param.UpdatedAt[1]
	}
	_, err := pb.GetProtoCoreClient().CluesBatchUpdate(middleware.ContextWithSpan(ctx), param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(pb.ServiceName, response.GetMicroDetail(err))
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, response.MsgSuccess, nil)
}

func ClueFilterGroup(ctx *gin.Context) error {
	if err := ClearCoreService(ctx); err != nil {
		// ClearCoreService 已经处理返回值，此处只需要结束函数
		return nil
	}
	var param = &pb.ClueListRequest{}
	ctxSpan := middleware.ContextWithSpan(ctx)
	rsp, err := pb.GetProtoCoreClient().ClueFilterGroup(ctxSpan, param, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError(pb.ServiceName, err)
	}
	return response.Gen(ctx).SendSuccess(pb.ServiceName, "Success", rsp)
}

// GetClueCount 线索数量统计
func GetClueCount(ctx *gin.Context) error {
	_, _, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError("clue", err)
	}

	groupID := cast.ToUint64(ctx.Param("group_id"))
	operateCompanyID := cast.ToUint64(ctx.Query("operate_company_id"))
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError("detect_assets", err)
	}

	status := cast.ToInt32(ctx.Query("status"))
	detectTaskID := cast.ToUint64(ctx.Query("detect_task_id"))
	fakeDetectTaskID := cast.ToUint64(ctx.Query("fake_detect_task_id"))

	req := &web.ClueCountRequest{
		GroupId:          groupID,
		UserId:           userID,
		CompanyId:        uint64(companyID),
		OperateCompanyId: operateCompanyID,
		Status:           status,
		DetectTaskId:     detectTaskID,
		FakeDetectTaskId: fakeDetectTaskID,
	}
	rsp, err := web.GetProtoClient().GetClueCount(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(30))
	if err != nil {
		return response.Gen(ctx).SendByError("clue", err)
	}

	return response.Gen(ctx).SendSuccess("clue", response.MsgSuccess, rsp)
}

// processClueListResponse 处理线索列表响应，确保null字段返回空数组
func processClueListResponse(rsp *web.ClueListWholeResponse) map[string]interface{} {
	result := make(map[string]interface{})

	// 处理Domain字段
	if rsp.Domain != nil {
		// 处理每个domain项的chain_list
		for _, item := range rsp.Domain {
			if item.ChainList == nil {
				item.ChainList = []*web.ClueChain{}
			}
		}
		result["domain"] = rsp.Domain
	} else {
		result["domain"] = []interface{}{}
	}

	// 处理Icp字段
	if rsp.Icp != nil {
		// 处理每个icp项的chain_list
		for _, item := range rsp.Icp {
			if item.ChainList == nil {
				item.ChainList = []*web.ClueChain{}
			}
		}
		result["icp"] = rsp.Icp
	} else {
		result["icp"] = []interface{}{}
	}

	// 处理Cert字段
	if rsp.Cert != nil {
		// 处理每个cert项的chain_list
		for _, item := range rsp.Cert {
			if item.ChainList == nil {
				item.ChainList = []*web.ClueChain{}
			}
		}
		result["cert"] = rsp.Cert
	} else {
		result["cert"] = []interface{}{}
	}

	// 处理Icon字段
	if rsp.Icon != nil {
		// 处理每个icon项的chain_list
		for _, item := range rsp.Icon {
			if item.ChainList == nil {
				item.ChainList = []*web.ClueChain{}
			}
		}
		result["icon"] = rsp.Icon
	} else {
		result["icon"] = []interface{}{}
	}

	// 处理Ip字段
	if rsp.Ip != nil {
		// 处理每个ip项的chain_list
		for _, item := range rsp.Ip {
			if item.ChainList == nil {
				item.ChainList = []*web.ClueChain{}
			}
		}
		result["ip"] = rsp.Ip
	} else {
		result["ip"] = []interface{}{}
	}

	return result
}

// GetCluesIndex 资产测绘-获取线索列表
func GetCluesIndex(ctx *gin.Context) error {
	var req web.ClueListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		return response.Gen(ctx).SendByError("clue", err)
	}
	// 手动从 ctx.Query 获取参数，验证 URL 参数是否被正确解析
	req.Keyword = ctx.Query("keyword")
	req.GroupId = cast.ToUint64(ctx.Query("group_id"))
	req.Status = cast.ToInt32(ctx.Query("status"))
	req.Type = cast.ToInt32(ctx.Param("type"))
	req.IsWhole = cast.ToInt32(ctx.Query("is_whole"))
	req.DetectTaskId = cast.ToUint64(ctx.Query("detect_task_id"))
	req.OperateCompanyId = cast.ToUint64(ctx.Query("operate_company_id"))
	req.Page = cast.ToInt64(ctx.Query("page"))
	req.PerPage = cast.ToInt64(ctx.Query("per_page"))
	// 获取用户ID和企业ID
	_, userID, companyID, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError("detect_assets", err)
	}
	req.UserId = userID
	req.CompanyId = uint64(companyID)

	if req.IsWhole == 1 {
		// 调用rpc
		rsp, err := web.GetProtoClient().WholeClueList(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return response.Gen(ctx).SendByError("clue", err)
		}

		// 处理返回数据，确保null字段返回空数组
		processedRsp := processClueListResponse(rsp)
		return response.Gen(ctx).SendSuccess("clue", response.MsgSuccess, processedRsp)
	} else {
		// 调用rpc
		rsp, err := web.GetProtoClient().NoWholeClueList(middleware.ContextWithSpan(ctx), &req, utils.SetRpcTimeoutOpt(30))
		if err != nil {
			return response.Gen(ctx).SendByError("clue", err)
		}
		return response.Gen(ctx).SendSuccess("clue", response.MsgSuccess, rsp)
	}
}

// GetFofaAssetsNum 根据线索计算fofa资产的数量
func GetFofaAssetsNum(ctx *gin.Context) error {
	// 获取线索ID
	idStr := ctx.Query("id")
	if idStr == "" {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "参数错误：缺少线索ID")
	}
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg(api.ServiceName, "参数错误：线索ID格式不正确")
	}
	_, userID, opCompanyId, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError("detect_assets", err)
	}

	// 构建请求参数
	req := &web.GetFofaAssetsNumRequest{
		Id:               id,
		OperateCompanyId: uint64(opCompanyId),
		UserId:           userID,
	}

	// 调用RPC方法
	rsp, err := web.GetProtoClient().GetFofaAssetsNum(middleware.ContextWithSpan(ctx), req, utils.SetRpcTimeoutOpt(60))
	if err != nil {
		return response.Gen(ctx).SendByError(web.ServiceName, err)
	}

	return response.Gen(ctx).SendSuccess(web.ServiceName, "Success", rsp)
}
