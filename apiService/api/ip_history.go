package api

import (
	"micro-service/apiService/middleware"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	corePb "micro-service/coreService/proto"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/utils"
	"net"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// IpHistory IP获取域名的解析记录
func IpHistory(ctx *gin.Context) error {
	// 参数验证
	var param struct {
		ID               string `form:"id" binding:"required"`
		OperateCompanyID int64  `form:"operate_company_id"`
		From             int32  `form:"from"`
		UserID           int64  `form:"user_id"`
		IsFromSearch     int32  `form:"is_from_search"`
	}

	if err := ctx.ShouldBindQuery(&param); err != nil {
		return response.Gen(ctx).SendByErrorMsg("ip_history", "参数错误!")
	}

	// 获取用户ID和企业ID
	_, userID, _, err := safe_company.GetSafeCompanyUser(ctx, param.OperateCompanyID)
	if err != nil {
		return response.Gen(ctx).SendByErrorMsg("ip_history", "参数错误!")
	}

	// 处理超管权限和搜索来源
	if isAdmin, _ := safe_company.IsAdmin(ctx); isAdmin && param.IsFromSearch > 0 {
		if param.UserID <= 0 {
			return response.Gen(ctx).SendByErrorMsg("ip_history", "用户参数错误!")
		}
		userID = uint64(param.UserID)
	}

	// 根据来源选择查询模型和提取IP
	var ip string
	if param.From == 0 {
		// 处理IPv6地址
		parts := strings.Split(param.ID, "_")
		if len(parts) > 1 && strings.Count(parts[1], ":") > 1 {
			// 补全IPv6地址
			ipv6 := completeIPV6(parts[1])
			if ipv6 == "" {
				return response.Gen(ctx).SendByErrorMsg("ip_history", "参数错误!")
			}
			ip = ipv6
		} else if len(parts) > 1 {
			ip = parts[1]
		} else {
			return response.Gen(ctx).SendByErrorMsg("ip_history", "参数错误!")
		}
	} else {
		// 从任务资产ID提取IP
		parts := strings.Split(param.ID, "_")
		if len(parts) > 1 {
			ip = parts[1]
		} else {
			return response.Gen(ctx).SendByErrorMsg("ip_history", "参数错误!")
		}
	}

	// 判断是本地化还是SAAS环境
	var result interface{}

	if cfg.IsLocalClient() {
		// 本地化通过HTTP调用SAAS
		url := "/api/v1/fofa/ip_domain_history"
		reqBody := map[string]interface{}{
			"ip":      []string{ip},
			"is_all":  true,
			"user_id": userID,
		}
		httpResult := make(map[string]interface{})
		httpErr := corePb.HttpClient(http.MethodPost, url, reqBody, &httpResult)
		if httpErr != nil {
			log.WithContextErrorf(ctx, "调用SAAS获取IP域名解析历史失败: %v", httpErr)
			return response.Gen(ctx).SendByError("ip_history", httpErr)
		}
		result = httpResult
	} else {
		// 通过RPC微服务调用
		req := &corePb.IpDomainHistoryRequest{
			UserId: uint64(userID),
			Ip:     []string{ip},
			IsAll:  true,
		}
		ctxSpan := middleware.ContextWithSpan(ctx)
		rsp, rpcErr := corePb.GetProtoCoreClient().IpDomainHistory(ctxSpan, req, utils.RpcTimeoutDur(10*time.Minute), microx.ServerTimeoutDur(9*time.Minute))
		if rpcErr != nil {
			log.WithContextErrorf(ctx, "获取IP域名解析历史失败: %v", rpcErr)
			return response.Gen(ctx).SendByError("ip_history", rpcErr)
		}
		result = rsp.Items
	}

	// 返回结果
	return response.Gen(ctx).SendSuccess("ip_history", response.MsgSuccess, result)
}

// 补全IPv6地址
func completeIPV6(ip string) string {
	if ip == "" {
		return ""
	}

	// 尝试解析IPv6地址
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return ""
	}

	// 返回标准格式的IPv6地址
	return parsedIP.String()
}
