package middleware

import (
	"context"
	"micro-service/pkg/utils"
	"net/http"

	"micro-service/pkg/log"

	"github.com/gin-gonic/gin"
	opentracing "github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"go-micro.dev/v4/metadata"
)

// WithTracer  tracer 中间件
func WithTracer() gin.HandlerFunc {
	return func(c *gin.Context) {
		md := make(map[string]string)
		spanCtx, _ := opentracing.GlobalTracer().Extract(opentracing.HTTPHeaders, opentracing.HTTPHeadersCarrier(c.Request.Header))
		sp := opentracing.GlobalTracer().StartSpan(c.Request.URL.Path, opentracing.ChildOf(spanCtx))
		defer sp.Finish()

		if err := opentracing.GlobalTracer().Inject(sp.Context(),
			opentracing.TextMap,
			opentracing.TextMapCarrier(md)); err != nil {
			log.Errorf(err.Error())
		}

		ctx := context.TODO()
		ctx = opentracing.ContextWithSpan(ctx, sp)
		ctx = metadata.NewContext(ctx, md)
		c.Set("api_trace_context", ctx)

		c.Next()

		statusCode := c.Writer.Status()
		ext.HTTPStatusCode.Set(sp, uint16(statusCode))
		ext.HTTPMethod.Set(sp, c.Request.Method)
		ext.HTTPUrl.Set(sp, c.Request.URL.EscapedPath())
		if statusCode >= http.StatusInternalServerError {
			ext.Error.Set(sp, true)
		}
	}
}

// ContextWithSpan 返回context
func ContextWithSpan(c *gin.Context) (ctx context.Context) {
	v, exist := c.Get("api_trace_context")
	if exist {
		ctx = v.(context.Context)
	}
	client, exist := c.Get("client_id_with_token")
	if exist {
		ctx = metadata.Set(ctx, "client_id", client.(string))
	}
	user, exist := c.Get("user_id_with_token")
	if exist {
		ctx = metadata.Set(ctx, "user_id", user.(string))
	}
	clientIP := utils.GetClientIP(c.Request)
	ctx = metadata.Set(ctx, "client_ip", clientIP)

	return
}
