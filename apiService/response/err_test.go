package response

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	micerr "go-micro.dev/v4/errors"
)

func TestDecodeMicroErr(t *testing.T) {
	cases := []struct {
		in    error
		b     bool
		msg   string
		codes []int
	}{
		{in: errors.New("err from std pkg"), b: false},
		{in: micerr.New("500", "internal", 500), b: false},
		{in: micerr.NotFound("404", "not found"), b: true, codes: []int{404}},
	}

	for _, v := range cases {
		b, _ := DecodeMicroErr(v.in, v.codes...)
		assert.Equal(t, v.b, b)
	}
}
