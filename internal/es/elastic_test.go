package es

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"testing"
	"time"
)

func TestNewElasitcBaseConf(t *testing.T) {
	esConf := &ElasticBaseConf{
		version:        6,
		url:            "http://106.75.5.32:9200/",
		indexName:      "fofapro",
		indexSubdomain: "fofapro_subdomain",
		typeSubdomain:  "subdomain",
	}
	conf := NewElasitcBaseConf(esConf.version, esConf.url, esConf.indexName, esConf.indexSubdomain, esConf.typeSubdomain)

	assert.Equal(t, esConf.indexName, conf.GetIndexName(""))
	assert.Equal(t, esConf.indexSubdomain, conf.GetIndexName("subdomain"))

	assert.Equal(t, "", conf.GetTypeName(""))
	assert.Equal(t, esConf.typeSubdomain, conf.GetTypeName("subdomain"))

	tmExp := time.Date(1, time.January, 1, 0, 0, 0, 0, time.UTC)
	tm, err := conf.getTime(map[string]interface{}{}, "lastupdatetime")
	assert.Equal(t, tmExp, tm)
	assert.NotNil(t, err)

	tm, err = conf.getTime(map[string]interface{}{
		"lastupdatetime": "-af",
	}, "lastupdatetime")
	assert.Equal(t, tmExp, tm)
	assert.NotNil(t, err)

	fmt.Println(tmExp.String())
	nowTime := time.Now().Format("2006-01-02 15:04:05")
	tm, err = conf.getTime(map[string]interface{}{
		"lastupdatetime": nowTime,
	}, "lastupdatetime")
	assert.Equal(t, nowTime, tm.Format("2006-01-02 15:04:05"))
	assert.Nil(t, err)
}

func TestElastic6(t *testing.T) {
	//esConf := &ElasticBaseConf{
	//	version:        6,
	//	url:            "http://127.0.0.1:9200/",
	//	indexName:      "fofapro_subdomain",
	//	indexSubdomain: "fofapro_subdomain",
	//	typeSubdomain:  "subdomain",
	//}
	//conf := NewElasitcBaseConf(esConf.version, esConf.url, esConf.indexName, esConf.indexSubdomain, esConf.typeSubdomain)
	//elastic := NewElastic(conf)
	//assert.Equal(t, esConf.version, elastic.GetVersion())
	//
	////GetElasticBaseConfig
	//esCnfExp := elastic.GetElasticBaseConfig()
	//assert.Equal(t, esConf.version, esCnfExp.version)
	//assert.Equal(t, esConf.url, esCnfExp.url)
	//assert.Equal(t, esConf.indexName, esCnfExp.indexName)
	//assert.Equal(t, esConf.indexSubdomain, esCnfExp.indexSubdomain)
	//assert.Equal(t, esConf.typeSubdomain, esCnfExp.typeSubdomain)
	//
	//fmt.Println(elastic.GetClient())
	//
	//_, err := elastic.Get("rg")
	//assert.NotNil(t, err)
	//
	//m, err := elastic.Get("www.promo.jianbing.com")
	//assert.Nil(t, err)
	//
	//isUpdate := elastic.NeedUpdate(m)
	//assert.True(t, isUpdate)
	//
	//elastic.UpdateCheckTime("clients.jianbing.com", m)

}
