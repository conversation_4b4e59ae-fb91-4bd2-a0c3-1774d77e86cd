package job

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"git.gobies.org/shared-platform/quickstore/internal/model"
	"github.com/elastic/go-elasticsearch/v6"
	"github.com/elastic/go-elasticsearch/v6/esapi"
	"go-micro.dev/v4/logger"
	"google.golang.org/protobuf/types/known/structpb"
	"strings"
	"time"
)

const TimeFormat = "2006-01-02 15:04:05"

type job struct {
	serviceOrSubdomainDocumentID string
	taskAssetDocumentID          string
	conf                         *model.Config
	elastic                      *elasticsearch.Client
	msg                          *rpcx.QuickStoreEvent
	buf                          bytes.Buffer
	source                       map[string]interface{}
}

func (j *job) Normal() error {
	req := j.msg.GetNormal()

	if req.Url != nil && *req.Url != "" {
		j.serviceOrSubdomainDocumentID = strings.TrimPrefix(*req.Url, "http://")
	} else {
		j.serviceOrSubdomainDocumentID = fmt.Sprintf("%s:%d", req.Ip, req.Port)
	}

	// 获取当前时间，并指定时区
	tt := time.Now()

	geoIP := new(rpcx.Geoip)
	geoIP.Location = &rpcx.Location{
		Lon: 0,
		Lat: 0,
	}

	j.source = map[string]interface{}{
		"ip":             req.Ip,
		"ipcnet":         req.Ipcnet,
		"port":           req.Port,
		"protocol":       req.Protocol,
		"base_protocol":  req.BaseProtocol,
		"is_ipv6":        req.IsIpv6,
		"time":           tt,
		"lastchecktime":  tt.Format(TimeFormat),
		"lastupdatetime": tt.Format(TimeFormat),
		"v":              5,
		"geoip":          geoIP,
		"honeypot_name":  "",
		"subbody":        "",
	}

	if j.msg.TaskInfo.Extra != nil {
		extra := new(structpb.Struct)
		err := j.msg.TaskInfo.Extra.UnmarshalTo(extra)
		if err != nil {
			logger.Warn("TaskInfo.Extra from protobuf to struct failed: ", err)
		}

		j.source["extra"] = extra
		em := extra.AsMap()
		if userID, ok := em["user_id"]; ok {
			j.serviceOrSubdomainDocumentID = fmt.Sprintf("%s_%s", userID, j.serviceOrSubdomainDocumentID)
		}
	}

	if req.Mac != "" {
		j.source["mac"] = req.Mac
	}

	if req.NetbiosName != "" {
		j.source["netbios_name"] = req.NetbiosName
	}

	if req.Geoip != nil && req.Geoip.CountryName != "" {
		j.source["geoip"] = req.Geoip
	}

	if req.Asn != nil && req.Asn.AsNumber > 0 {
		j.source["asn"] = req.Asn
	}

	if len(req.Appserver) > 0 {
		j.source["appserver"] = req.Appserver
	}

	if len(req.Os) > 0 {
		j.source["os"] = req.Os
	}

	if len(req.Modal) > 0 {
		j.source["modal"] = req.Modal
	}

	if len(req.Language) > 0 {
		j.source["language"] = req.Language
	}

	if len(req.Version) > 0 {
		j.source["version"] = req.Version
	}

	if req.Server != nil && *req.Server != "" {
		j.source["server"] = req.Server
	}

	if req.Product != nil {
		j.source["product"] = req.Product
	}

	if req.RuleTags != nil {
		j.source["rule_tags"] = req.RuleTags
	}

	if req.SubBody != "" {
		j.source["subbody"] = req.SubBody
	}

	if req.Port == 0 || j.msg.TaskInfo.TaskType == constant.TaskTypePing {
		//这是 ping 扫描结果，如果task_assets 不存在则插入
		return j.taskAssetPing()
	}

	j.wrapServiceOrSubdomainElasticRequestBody()
	return j.wrapTaskAsset()
}

func (j *job) FullSiteCrawler() error {
	logger.Infof("Received QuickStore.FullSiteCrawler event. %v", j.msg)
	documentID := fmt.Sprintf("%x", md5.Sum([]byte(j.msg.GetFullSiteCrawler().Hash)))
	t := time.Now().Format(TimeFormat)
	extraHeaders := new(structpb.Struct)
	err := j.msg.GetFullSiteCrawler().ExtraHeaders.UnmarshalTo(extraHeaders)
	eh := ""
	if err != nil {
		logger.Warnf("extraHeaders any to struct error", err, j.msg.GetFullSiteCrawler().ExtraHeaders)
	} else {
		marshalJSON, err := extraHeaders.MarshalJSON()

		if err != nil {
			logger.Warnf("extraHeaders any to struct error", err, j.msg.GetFullSiteCrawler().ExtraHeaders)
		}
		eh = string(marshalJSON)
	}

	m := map[string]interface{}{
		"v":              5,
		"url_hostinfo":   j.msg.GetFullSiteCrawler().Url,
		"ip":             j.msg.GetFullSiteCrawler().Ip,
		"is_ipv6":        j.msg.GetFullSiteCrawler().IsIpv6,
		"hostinfo":       j.msg.GetFullSiteCrawler().HostInfo,
		"type":           j.msg.GetFullSiteCrawler().Type,
		"params":         j.msg.GetFullSiteCrawler().Params,
		"data":           j.msg.GetFullSiteCrawler().Data,
		"method":         j.msg.GetFullSiteCrawler().Method,
		"post_data_type": j.msg.GetFullSiteCrawler().PostDataType,
		"hash":           j.msg.GetFullSiteCrawler().Hash,
		"extra_headers":  eh,
		"content_type":   j.msg.GetFullSiteCrawler().ContentType,
		"referer":        j.msg.GetFullSiteCrawler().Referer,
		"state":          j.msg.GetFullSiteCrawler().State,
		"status_code":    j.msg.GetFullSiteCrawler().StatusCode,
		"path":           j.msg.GetFullSiteCrawler().Path,
		"dir":            j.msg.GetFullSiteCrawler().Dir,
		"file":           j.msg.GetFullSiteCrawler().File,
		"lastchecktime":  t,
		"lastupdatetime": t,
	}

	if j.msg.GetFullSiteCrawler().U != nil {
		m["subdomain_host"] = j.msg.GetFullSiteCrawler().U.HostInfo
	}

	bodyMap := make(map[string]interface{})

	bodyMap["doc"] = m
	bodyMap["doc_as_upsert"] = true

	body, err := json.Marshal(bodyMap)

	if err != nil {
		return err
	}

	request := esapi.UpdateRequest{
		Index:        j.conf.Elastic.SiteURLIndex,
		DocumentType: j.conf.Elastic.SiteURLType,
		Body:         bytes.NewReader(body),
		DocumentID:   documentID,
	}

	do, err := request.Do(context.Background(), j.elastic)

	if err != nil {
		return err
	}

	if do.IsError() {
		return errors.New(do.String())
	}

	defer do.Body.Close()
	logger.Info("elastic full site crawler result:", do.String())
	return nil
}

type Job interface {
	Normal() error
	FullSiteCrawler() error
}

func NewTask(
	conf *model.Config,
	elastic *elasticsearch.Client,
	msg *rpcx.QuickStoreEvent,
) Job {
	return &job{
		conf:    conf,
		elastic: elastic,
		msg:     msg,
		source:  make(map[string]interface{}),
	}
}
