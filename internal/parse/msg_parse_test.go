package parse

import (
	"baimaohui/portscan_new/internal/util"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestParseMsgFoeye(t *testing.T) {
	var feTask FoeyeTaskMsg

	var parseStub ParseMsgStub
	parseStub = NewMsgParse(util.PTFoeye)
	sendEth := ""

	msg := "[{\"bandwidth\":\"150\",\"blacklist\":\"************\",\"deep_get_mac\":false,\"ip_list\":\"**********/30\",\"nmap_scan\":false,\"ping_scan\":false,\"deep_get_os\":false,\"ports\":\"21,22,23\",\"protocol_update_cycle\":0,\"unknown_protocol_indb\":false}]"
	msgInst, err := parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)
	feTask = msgInst.(FoeyeTaskMsg)
	assert.Equal(t, "************", feTask.Blacklist)
	//assert.Equal(t, "", feTask.DomainInfo)
	assert.Equal(t, 0, feTask.ProtocolUpdateCycle)
	assert.Equal(t, false, feTask.UnknownProtocolIndb)
	assert.Equal(t, false, feTask.CanOpenPing)
	assert.Equal(t, false, feTask.CanDeepGetOS)
	assert.Equal(t, "", feTask.GatewayMAC)
	assert.Equal(t, false, feTask.CanDeepGetMAC)

	msg = "[{\"bandwidth\":\"150\",\"blacklist\":\"\",\"deep_get_mac\":true,\"ip_list\":\"**********/30\",\"nmap_scan\":true,\"ping_scan\":true,\"deep_get_os\":true,\"ports\":\"21,22,23\",\"protocol_update_cycle\":1,\"unknown_protocol_indb\":true}]"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)
	feTask = msgInst.(FoeyeTaskMsg)
	assert.Equal(t, "", feTask.Blacklist)
	//assert.Equal(t, "", feTask.DomainInfo)
	assert.Equal(t, 1, feTask.ProtocolUpdateCycle)
	assert.Equal(t, true, feTask.UnknownProtocolIndb)
	assert.Equal(t, true, feTask.CanOpenPing)
	assert.Equal(t, true, feTask.CanDeepGetOS)
	assert.Equal(t, "", feTask.GatewayMAC)
	assert.Equal(t, true, feTask.CanDeepGetMAC)

	msg = "[{\"bandwidth\":\"150\""
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.NotNil(t, err)

	msg = "{}"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.NotNil(t, err)
}

func TestParseMsgFofa(t *testing.T) {
	var btmMsg BaseTaskMsg

	var parseStub ParseMsgStub
	parseStub = NewMsgParse(util.PTFofa)
	sendEth := "ifcfg-11"
	msg := ""

	msg = "[{\"bandwidth\":\"150\",\"ip_list\":\"**********/30\",\"ports\":\"21,22,23\", \"task_id\": \"10010\",\"scan_mode\":\"common\"}]"
	msgInst, err := parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)
	btmMsg = msgInst.(BaseTaskMsg)
	assert.Equal(t, "150", btmMsg.Bandwidth)
	assert.Equal(t, "**********/30", btmMsg.IpList)
	assert.Equal(t, "", btmMsg.Hosts)
	assert.Equal(t, "21,22,23", btmMsg.Ports)
	assert.Equal(t, "10010", btmMsg.TaskId)
	assert.Equal(t, 10010, btmMsg.Tid)
	assert.Equal(t, "common", btmMsg.ScanMode)
	assert.Equal(t, "ifcfg-11", btmMsg.SendEth)
	assert.False(t, btmMsg.IsIPv6)
	assert.Equal(t, "", btmMsg.ResumeFilename)

	msg = "[{\"bandwidth\":\"150\",\"ip_list\":\"**********/30\",\"ports\":\"21,22,23\", \"task_id\": \"10010\"}]"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)
	btmMsg = msgInst.(BaseTaskMsg)
	assert.Equal(t, "150", btmMsg.Bandwidth)
	assert.Equal(t, "**********/30", btmMsg.IpList)
	assert.Equal(t, "", btmMsg.Hosts)
	assert.Equal(t, "21,22,23", btmMsg.Ports)
	assert.Equal(t, "10010", btmMsg.TaskId)
	assert.Equal(t, 10010, btmMsg.Tid)
	assert.Equal(t, "common", btmMsg.ScanMode)
	assert.Equal(t, "ifcfg-11", btmMsg.SendEth)
	assert.False(t, btmMsg.IsIPv6)
	assert.Equal(t, "", btmMsg.ResumeFilename)

	msg = "[{\"bandwidth\":\"150\""
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.NotNil(t, err)

	msg = "{}"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.NotNil(t, err)
}

func TestNewMsgParse(t *testing.T) {
	var parseStub ParseMsgStub
	parseStub = NewMsgParse(100)
	assert.Nil(t, parseStub)
}
