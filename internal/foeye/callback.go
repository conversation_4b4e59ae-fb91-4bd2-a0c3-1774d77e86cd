package foeye

import (
	"context"
	"encoding/base64"
	"fmt"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	"net/url"
	"time"

	"github.com/imroc/req/v3"
	"go-micro.dev/v4/logger"
)

const (
	StatusStarting = "1"
	StatusRunning  = "2"
	StatusStopping = "3"
	StatusStopped  = "4"
	StatusSuccess  = "5"
	StatusFailed   = "6"
	StatusPaused   = "7"
)

type TaskStateCallback struct {
	config *Config
	http   *req.Client
	m      map[string]string
}

func NewTaskStateCallback(config *Config) *TaskStateCallback {
	return &TaskStateCallback{
		config: config,
		http:   req.NewClient(),
		m: map[string]string{
			constant.StateRunning:     StatusRunning,
			constant.StateStopping:    StatusStopping,
			constant.StateStopped:     StatusStopped,
			constant.StateFinished:    StatusSuccess,
			constant.StateError:       StatusFailed,
			constant.StatePaused:      StatusPaused,
			constant.StateDispatching: StatusStarting,
		},
	}
}

func (t *TaskStateCallback) NotifyStateChanged(ctx context.Context, taskID string, state, message string) {
	logger.Infof("notify state changed. task:%s state:%s", taskID, state)
	if status, ok := t.m[state]; ok {
		t.notifyStatusCallback(ctx, taskID, status, message)
	}
}

func (t *TaskStateCallback) NotifyProgress(ctx context.Context, taskID string, progress float32, workingOn, remainTime string) {
	t.notifyProgressCallback(ctx, taskID, progress, workingOn, remainTime)
}

func (t *TaskStateCallback) notifyStatusCallback(ctx context.Context, taskID, state, message string) {
	if !t.config.ThirdSystem.Enabled {
		return
	}
	params := url.Values{}
	params.Add("task_id", taskID)
	params.Add("state", state)
	params.Add("message", base64.StdEncoding.EncodeToString([]byte(message)))

	u := t.config.ThirdSystem.GetPathNotifyStatus(params)

	interval := time.Second * time.Duration(t.config.ThirdSystem.FailRetryInterval)
	resp, err := t.http.R().SetRetryCount(t.config.ThirdSystem.FailRetryTimes).SetRetryFixedInterval(interval).Get(u)
	if err != nil || resp.StatusCode != 200 {
		logger.Warnf("notify status callback failed. taskID=%s url=%s", taskID, u)
		return
	}
	logger.Infof("notify status callback successful. taskID=%s url=%s", taskID, u)
}

func (t *TaskStateCallback) notifyProgressCallback(ctx context.Context, taskID string, progress float32, workingOn, remainTime string) {
	if !t.config.ThirdSystem.Enabled {
		return
	}
	params := url.Values{}
	params.Add("task_id", taskID)
	params.Add("state", StatusRunning)
	params.Add("host_completed", "0")
	params.Add("progress", fmt.Sprintf("%.2f", progress))
	// TODO 计算时间
	params.Add("remain_time", remainTime)
	params.Add("scan_info", workingOn)

	u := t.config.ThirdSystem.GetPathNotifyProgress(params)

	resp, err := t.http.R().Get(u)
	if err != nil || resp.StatusCode != 200 {
		logger.Warnf("notify progress callback failed. taskID=%s url=%s", taskID, u)
		return
	}
	logger.Infof("notify progress callback successful. taskID=%s url=%s", taskID, u)
}
