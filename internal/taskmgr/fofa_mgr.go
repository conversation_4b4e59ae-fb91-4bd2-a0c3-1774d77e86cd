package taskmgr

import (
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/util"
	"github.com/pkg/errors"
	"log"
	"strings"
)

type FofaTaskMgr struct {
	attr parse.BaseTaskMsg
	*TaskerMgr
}

func NewFofaTaskMgr(tm *TaskerMgr) *FofaTaskMgr {
	ftm := &FofaTaskMgr{
		TaskerMgr: tm,
	}
	hostName, _ := util.GetHost()
	ftm.hostname = hostName

	return ftm
}

func (ftm *FofaTaskMgr) GetAttr() interface{} {
	return ftm.attr
}

func (ftm *FofaTaskMgr) StartWholeTask(msg interface{}) error {
	defer ftm.wholeTaskFinish()

	if tmpAttr, ok := msg.(parse.BaseTaskMsg); ok {
		ftm.attr = tmpAttr

		// 支持微内核参数处理
		if len(ftm.attr.Hosts) <= 0 {
			ftm.attr.Hosts = ftm.attr.IpList
		}
	} else {
		return errors.New("start_whole_task msg change failed")
	}

	ftm.WholeTaskInit()

	exits := ftm.getUndoneEngine()
	if !exits {
		log.Println("  start_whole_task all task finished")
		return nil
	}

	log.Println("   start_whole_task curengine:", ftm.CurEngine)

	var err error
	err = ftm.runSubTask()
	log.Printf("  start_whole_task subtask finish  err:%v\n", err)

	return nil
}

func (ftm *FofaTaskMgr) WholeTaskInit() {
	// 生成运行引擎
	ftm.generateEngines()
	log.Println("  start_whole_task task:", ftm.attr)

	// 设置进度比率信息
	ftm.Engines[0].ProgressRate = 1.0
	ftm.Engines[0].MinProgress = 0.0
	ftm.Engines[0].MaxProgress = 99.9

	return
}

func (ftm *FofaTaskMgr) wholeTaskFinish() {
	ftm.dbAct.DeleteKey(ftm.dbAct.GetKeyName("stop_task"))
	log.Println("  task_mgr whole finish task", ftm.attr.TaskId)

	// 重置状态
	ftm.CurEngine = nil
	ftm.Engines = nil
}

// 生成运行引擎
func (ftm *FofaTaskMgr) generateEngines() {
	if ftm.getDefaultEngine() == task.ETNmap {
		if "ip_alive" == ftm.attr.ScanMode {
			ftm.appendTaskEngine(task.ETNmapPing)
			return
		}

		// 最少得有一个默认引擎
		ftm.appendTaskEngine(task.ETNmap)
	} else {
		if ftm.attr.CanTreckScan {
			ftm.appendTaskEngine(task.ETTreckScan)
			return
		}

		if "ip_alive" == ftm.attr.ScanMode {
			ftm.appendTaskEngine(task.ETMasscanPing)
			return
		}

		// 最少得有一个默认引擎
		ftm.appendTaskEngine(task.ETMasscan)
	}
}

// 获取默认引擎，如果是二层或IPv6，固定为nmap
func (ftm *FofaTaskMgr) getDefaultEngine() task.EngineType {
	// IPv6默认为nmap
	if strings.Contains(ftm.attr.Hosts, ":") || ftm.attr.IsIPv6 {
		return task.ETNmap
	}

	return task.ETMasscan
}

// 在Redis里面创建带tid的键
func (ftm *FofaTaskMgr) redisTaskInit(tid int) {
	ftm.dbAct.DelCurrentTid(ftm.hostname)
	ftm.dbAct.AddCurrentTid(ftm.hostname, tid)
}

func (ftm *FofaTaskMgr) MgrHandlePaused() (<-chan [][4]string, error) {
	taskid := ftm.dbAct.GetCurrentTid(ftm.hostname)
	log.Printf("task_mgr handlePaused  hostname:%v taskid:%v\n", ftm.hostname, taskid)

	// 先得创建对象
	if ftm.scantask == nil {
		ftm.scantask = task.NewScantask(ftm.taskBaseConf)
	}

	// 处理fofa暂停文件
	ch, err := ftm.scantask.HandlePaused(ftm.dbAct.GetBlackIps(), taskid)
	if err != nil {
		return nil, errors.Wrap(err, "handle paused task failed")
	}

	return ch, nil
}

func (ftm *FofaTaskMgr) runSubTask() error {
	ftm.redisTaskInit(ftm.attr.Tid)

	if ftm.CurEngine.Engine == task.ETMasscan ||
		ftm.CurEngine.Engine == task.ETMasscanPing ||
		ftm.CurEngine.Engine == task.ETNmap ||
		ftm.CurEngine.Engine == task.ETTreckScan {
		_, err := ftm.runScan()
		if err != nil {
			return err
		}
	} else {
		log.Println("invalid engine:", ftm.CurEngine.Engine)
	}

	return nil
}

func (ftm *FofaTaskMgr) runScan() (bool, error) {
	ftm.taskBaseConf.SetEngType(ftm.CurEngine.Engine)
	ftm.scantask = task.NewScantask(ftm.taskBaseConf)
	if ftm.scantask == nil {
		return false, errors.New("scantask create failed")
	}
	ftm.scantask.SetScanEngine(ftm.CurEngine)

	// 有IPv6地址的，必须启动IPv6扫描
	if strings.Contains(ftm.attr.Hosts, ":") {
		ftm.scantask.SetScanIPv6(true)
	} else {
		ftm.scantask.SetScanIPv6(ftm.attr.IsIPv6)
	}

	var ch <-chan [][4]string
	var err error
	blackIpList := ftm.getFullBlackList(ftm.attr.Ports)
	log.Println("get full blacklist  tid:", ftm.attr.TaskId, "blacklist:", blackIpList)

	if len(ftm.attr.ResumeFilename) > 0 && ftm.scantask.ResumeFileExists() { // 存在暂停文件的时候继续
		ch, err = ftm.scantask.HandlePaused(blackIpList, ftm.attr.Tid)
		if err != nil {
			return false, errors.Wrap(err, "fofa_mgr resume scan failed")
		}
	} else {
		// 先写文件再扫描
		tmpHostFilename := ftm.taskBaseConf.GetDir() + "/scan_hostlist.txt"
		err = ftm.WriteFile(tmpHostFilename, ftm.attr.Hosts)
		if err != nil {
			return false, errors.Wrap(err, "write host file failed")
		}

		// 禁扫文件
		tmpBlackFilename := ftm.taskBaseConf.GetDir() + "/black_iplist.txt"
		allBlackIpList := ftm.taskBaseConf.MergeExcludeIps(blackIpList)
		err = ftm.WriteFile(tmpBlackFilename, allBlackIpList)
		if err != nil {
			return false, errors.Wrap(err, "write black ip file failed")
		}

		ch, err = ftm.scantask.RunFile(ftm.attr.Bandwidth, tmpHostFilename, ftm.attr.Ports,
			blackIpList, ftm.attr.SendEth, tmpBlackFilename, ftm.attr.Tid, 1)
		if err != nil {
			return false, errors.Wrap(err, "fofa_mgr run scan failed")
		}
	}

	ftm.foundResultCB(ch, nil)

	return true, nil
}

// fofa不需要发现IP信息的回调
func (ftm *FofaTaskMgr) SetExecCallback(fne task.ExecCallback, fnp task.ProgressCallback, clpcb task.ChkLastProcedCallback) {
	ftm.taskBaseConf.SetCallback(nil, fnp, clpcb)
}

// fofa不需要这个
func (ftm *FofaTaskMgr) GetMsgDomains() ([]string, map[string]interface{}) {
	log.Fatal("fofa taskmgr get msg domains not implement")

	return nil, nil
}

func (ftm *FofaTaskMgr) Stop() error {
	if !ftm.scantask.IsStopSt() {
		log.Println("  task_mgr send stop task to process")

		err := ftm.scantask.Stop() // 不能使用协程，必须等待停止完成
		if err != nil {
			return err
		}
	}

	return nil
}

func (ftm *FofaTaskMgr) IsStoped() bool {
	return ftm.scantask.IsStopSt()
}

/*
获取全部的黑名单列表
  1. 先从全局黑名单里面读取
  2. 再从每个端口配置的黑名单里面读取
*/
func (ftm *FofaTaskMgr) getFullBlackList(port string) string {
	blackIpList := ftm.dbAct.GetBlackIps()

	// 扫描多个端口的时候不处理
	if strings.Contains(port, ",") {
		return blackIpList
	}

	// 如果是单端口则读取与端口相关的黑名单
	portBlacklist, err := ftm.dbAct.GetHashValue(ftm.dbAct.GetKeyName("port_blacklist"), port)
	if err != nil {
		log.Println("get full blacklist failed:", err)
		return blackIpList
	}

	if len(portBlacklist) > 0 {
		if len(blackIpList) > 0 && string(blackIpList[len(blackIpList)-1]) != "," && string(portBlacklist[0]) != "," {
			blackIpList += ","
		}
		blackIpList += portBlacklist
	}

	return blackIpList
}
