package taskmgr

import (
	"baimaohui/portscan_new/internal/conf"
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/db"
	"baimaohui/portscan_new/internal/parse"
	"baimaohui/portscan_new/internal/task"
	"baimaohui/portscan_new/internal/util"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"log"
	"os"
	"testing"
)

func initRedisMocks() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestNewFofaTaskMgr(t *testing.T) {
	tmpHostname, _ := util.GetHost()
	curDir, _ := os.Getwd()

	mockRds := initRedisMocks()
	log.Println("  mock redis addr: ", mockRds.Addr())

	var cfg conf.Config
	cfg.Redis = new(config.RedisConf)
	cfg.Redis.Addr = mockRds.Addr()
	cfg.Masscan.Dir = curDir

	dbBaseConf := db.NewDbBaseConfig(db.DBTRedis, "fofa:", "stop_task_flag", "exec_ip_info", "scan_task",
		cfg.Redis)
	dbAct := db.NewDb(dbBaseConf)
	assert.NotNil(t, dbAct)
	assert.NotNil(t, dbAct.GetConf())

	var mgrOpt []MgrOption
	taskBaseCfg := task.NewScantaskBaseConf(0, cfg.Masscan.Dir, tmpHostname, cfg.Masscan.BlackipList,
		cfg.Masscan.SourcePort, cfg.Worker.BatchSize, cfg.Worker.SameIpMaxCount, true, true, false)
	mgrOpt = append(mgrOpt, SetTaskBaseConf(taskBaseCfg))
	taskMgr := NewTaskMgr(dbAct, mgrOpt...)
	assert.NotNil(t, taskMgr)
	fofaMgr := NewTaskObjMgr(util.PTFofa, taskMgr)
	assert.NotNil(t, fofaMgr)

	fofaMgr.SetExecCallback(nil, nil, nil)

	_, err := fofaMgr.MgrHandlePaused()
	assert.Nil(t, err)

	//var btmMsg parse.BaseTaskMsg
	var parseStub parse.ParseMsgStub
	parseStub = parse.NewMsgParse(util.PTFofa)
	sendEth := "ifcfg-11"
	msg := "[{\"bandwidth\":\"150\",\"ip_list\":\"127.0.0.1\",\"ports\":\"21,22,23\", \"task_id\": \"10010\",\"scan_mode\":\"common\"}]"
	msgInst, err := parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)

	// 正常扫描
	err = fofaMgr.StartWholeTask(msgInst)
	assert.Nil(t, err)

	// 单端口扫描
	err = dbAct.SetHashValue(dbAct.GetKeyName("port_blacklist"), "21", "***************")
	assert.Nil(t, err)
	msg = "[{\"bandwidth\":\"150\",\"ip_list\":\"127.0.0.1\",\"ports\":\"21\", \"task_id\": \"10010\",\"scan_mode\":\"common\"}]"
	msgInst, err = parseStub.ParseMsg([]byte(msg), sendEth)
	assert.Nil(t, err)

	err = fofaMgr.StartWholeTask(msgInst)
	assert.Nil(t, err)

	// ip存活扫描
	btmMsg := msgInst.(parse.BaseTaskMsg)
	btmMsg.ScanMode = "ip_alive"
	err = fofaMgr.StartWholeTask(btmMsg)
	assert.Nil(t, err)

	// ipv6
	btmMsg.IpList = "2406:da18:880:3800:3cf7:d90b:9468:f4a6"
	err = fofaMgr.StartWholeTask(btmMsg)
	assert.Nil(t, err)

	// error msg
	err = fofaMgr.StartWholeTask(nil)
	assert.NotNil(t, err)

	assert.NotNil(t, fofaMgr.GetAttr())

	assert.False(t, fofaMgr.IsStoped())

	remScanHostlistFile := curDir + "/scan_hostlist.txt"
	err = util.DeleteFile(remScanHostlistFile)
	assert.Nil(t, err)

	remBlackIplistFile := curDir + "/black_iplist.txt"
	err = util.DeleteFile(remBlackIplistFile)
	assert.Nil(t, err)
}
