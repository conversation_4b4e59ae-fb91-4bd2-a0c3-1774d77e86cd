package service

import (
	"go-micro.dev/v4"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/registry"
)

type Node struct {
	node           *registry.Node
	Name           string            // 节点名称
	NodeAddress    string            // 节点地址
	ServiceName    string            // 服务名称
	ServiceVersion string            // 服务版本
	Metadata       map[string]string // 元数据信息
}

type NodeMonitor struct {
	srv     micro.Service
	exit    chan bool
	watcher registry.Watcher
	nodes   map[string]map[string]*Node
}

func NewNodeMonitor(srv micro.Service) *NodeMonitor {
	return &NodeMonitor{
		srv:   srv,
		exit:  make(chan bool),
		nodes: make(map[string]map[string]*Node),
	}
}

func (nm *NodeMonitor) Start() error {
	var err error
	nm.watcher, err = nm.srv.Options().Registry.Watch()
	if err != nil {
		logger.Warn("watch failed.", err)
		return err
	}

	go func() {
		logger.Info("node monitor watcher started")
		for {
			result, err := nm.watcher.Next()
			if err != nil {
				logger.Warn("node monitor ", err)
				break
			}
			logger.Infof("watch result %s %s", result.Action, result.Service.Name)
			switch result.Action {
			case "update":
				fallthrough
			case "create":
				for _, node := range result.Service.Nodes {
					if n, ok := nm.nodes[result.Service.Name]; ok {
						n[node.Id] = &Node{node: node, ServiceName: result.Service.Name, ServiceVersion: result.Service.Version, NodeAddress: node.Address, Metadata: node.Metadata}
					} else {
						n = make(map[string]*Node)
						n[node.Id] = &Node{node: node, ServiceName: result.Service.Name, ServiceVersion: result.Service.Version, NodeAddress: node.Address, Metadata: node.Metadata}
						nm.nodes[result.Service.Name] = n
					}
				}
			case "delete":
				if n, ok := nm.nodes[result.Service.Name]; ok {
					for _, node := range result.Service.Nodes {
						delete(n, node.Id)
					}
				}
			default:
			}
		}
	}()
	return nil
}

func (nm *NodeMonitor) Stop() {
	if nm.watcher != nil {
		nm.watcher.Stop()
	}
	select {
	case <-nm.exit:
		return
	default:
		close(nm.exit)
	}
}

func (nm *NodeMonitor) GetNodes(service string) []*Node {
	if n, ok := nm.nodes[service]; ok {
		var nodes = make([]*Node, 0)
		for _, node := range n {
			nodes = append(nodes, node)
		}
		return nodes
	}
	return []*Node{}
}
