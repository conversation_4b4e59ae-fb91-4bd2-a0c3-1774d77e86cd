package service

import (
	"api/internal/license"
	"api/internal/repository"
	rpcx "git.gobies.org/shared-platform/foscan/pkg/proto"
	"go-micro.dev/v4"
	"reflect"
	"testing"
)

func TestNewService(t *testing.T) {
	type args struct {
		dispatcher   rpcx.DispatcherTaskService
		license      *license.License
		repo         repository.Repository
		rawGrab      rpcx.RawGrabService
		dataAnalysis rpcx.DataAnalysisService
		micro        micro.Service
	}
	tests := []struct {
		name string
		args args
		want Service
	}{
		{
			args: args{},
			want: &service{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewService(tt.args.dispatcher, tt.args.license, tt.args.repo, tt.args.rawGrab, tt.args.dataAnalysis, tt.args.micro); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("NewService() = %v, want %v", got, tt.want)
			}
		})
	}
}
