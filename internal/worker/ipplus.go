package worker

import (
	"fmt"
	"net"
	"strconv"
	"strings"
)

type AwIpInfo struct {
	Ip        string `json:"ip"`
	Continent string `json:"continent"` //
	Country   string `json:"country"`
	Areacode  string `json:"areacode"`
	Accuracy  string `json:"accuracy"`
	Scene     string `json:"scene"`
	User      string `json:"user"`
	UserType  string `json:"user_type"`
	Zipcode   string `json:"zipcode"`

	MultiAreas []*AwAreaInfo `json:"multiAreas"`
	Isp        string        `json:"isp"`
	Asnumber   string        `json:"asnumber"`
	Timezone   string        `json:"timezone"`
}

type AwAreaInfo struct {
	Prov     string  `json:"province"` // 中国台湾
	City     string  `json:"city"`     // 台北市
	District string  `json:"district"` // 南港区
	Latbd    float64 `json:"latbd"`    // 25.045005
	Lngbd    float64 `json:"lngbd"`    // 121.625398
	Latwgs   float64 `json:"latwgs"`   // 25.042322
	Lngwgs   float64 `json:"lngwgs"`   // 121.615094

	Radius string `json:"radius"` // 0.1929
}

type AwLocat struct {
	Latitude  float64 `json:"lat"`
	Longitude float64 `json:"lon"`
}
type AwGeoInfo struct {
	ContinentCode string `json:"continent_code"`
	CountryName   string `json:"country_name"`
	CountryCode2  string `json:"country_code2"`
	CountryCode3  string `json:"country_code3"`
	District      string `json:"district"`
	PostCode      string `json:"postal_code"`
	Scene         string `json:"scene"`
	User          string `json:"user"`
	UserType      string `json:"user_type"`
	Isp           string `json:"isp"`
	Asnumber      int    `json:"asnumber"`

	MultiAreas     []*AwAreaInfo `json:"multiAreas"`
	RealRegionName string        `json:"real_region_name"`
	RegionName     string        `json:"region_name"`
	City           string        `json:"city_name"`
	Latitude       float64       `json:"latitude"`
	Longitude      float64       `json:"longitude"`
	Location       *AwLocat      `json:"location"`
	DmaCode        string        `json:"dma_code"`
	Timezone       string        `json:"timezone"`
}

func GetAwGeoInfo(ip string) (result *AwGeoInfo, err error) {
	var info *AwIpInfo
	info, err = genIpInfo(ip)
	if err != nil {
		return
	}

	if info.Continent == "保留IP" {
		return
	}

	var loc = &AwLocat{}
	asn, _ := strconv.Atoi(info.Asnumber)
	var geoInfo = &AwGeoInfo{
		ContinentCode: info.Continent,
		CountryName:   info.Country,
		CountryCode2:  info.Areacode,
		PostCode:      info.Zipcode,
		Scene:         info.Scene,
		User:          info.User,
		UserType:      info.UserType,
		Isp:           info.Isp,
		Asnumber:      asn,
		MultiAreas:    info.MultiAreas,
	}
	//if info.Timezone == "UTC+8" {
	//	geoInfo.Timezone = "Asia/Shanghai"
	//}
	geoInfo.Timezone = info.Timezone
	if len(info.MultiAreas) > 0 {
		area := info.MultiAreas[0]
		loc.Latitude = area.Latbd
		loc.Longitude = area.Lngbd
		geoInfo.Latitude = area.Latbd
		geoInfo.Longitude = area.Lngbd
		geoInfo.Location = loc
		geoInfo.RealRegionName = area.Prov
		geoInfo.City = area.City
		geoInfo.District = area.District
	}
	result = geoInfo
	//var geo []byte
	//var rm = map[string]*AwGeoInfo{ip: geoInfo}
	//geo, err = json.Marshal(&rm)
	//result = string(geo)
	return
}

func genIpInfo(ip string) (info *AwIpInfo, err error) {
	var record interface{}
	err = awIpDb.Lookup(net.ParseIP(ip), &record)
	if err != nil {
		return
	}
	var infM = record.(map[string]interface{})
	info = &AwIpInfo{Ip: ip}
	for k, v := range infM {
		if k != "multiAreas" {
			var val = B2S(v.([]uint8))
			switch k {
			case "areacode":
				info.Areacode = val
			case "user":
				info.User = val
			case "user_type":
				info.UserType = val
			case "zipcode":
				info.Zipcode = val
			case "timezone":
				info.Timezone = val
			case "accuracy":
				info.Accuracy = val
			case "asnumber":
				info.Asnumber = val
			case "continent":
				info.Continent = val
			case "country":
				info.Country = val
			case "isp":
				info.Isp = val
			case "scene":
				if strings.Contains(val, "已路由") || strings.Contains(val, "已分配") {
					info.Scene = "已使用"
				} else {
					info.Scene = val
				}
			default:
				fmt.Println(k, val)
			}
		} else {
			areas := make([]*AwAreaInfo, 0)
			for _, v1 := range v.([]interface{}) {
				var area = &AwAreaInfo{}
				for i, ma := range v1.(map[string]interface{}) {
					var valm = B2S(ma.([]uint8))
					switch i {
					case "city":
						area.City = valm
					case "district":
						area.District = valm
					case "latbd":
						latbd, _ := strconv.ParseFloat(valm, 64)
						area.Latbd = latbd
					case "latwgs":
						latwgs, _ := strconv.ParseFloat(valm, 64)
						area.Latwgs = latwgs
					case "lngbd":
						lonbd, _ := strconv.ParseFloat(valm, 64)
						area.Lngbd = lonbd
					case "lngwgs":
						lngwgs, _ := strconv.ParseFloat(valm, 64)
						area.Lngwgs = lngwgs
					case "prov":
						area.Prov = valm
					case "radius":
						area.Radius = valm
					default:
						fmt.Println(i, valm)
					}
				}
				areas = append(areas, area)
			}
			info.MultiAreas = areas
		}
	}
	return
}

func B2S(bs []uint8) string {
	ba := []byte{}
	for _, b := range bs {
		ba = append(ba, byte(b))
	}
	return string(ba)
}
