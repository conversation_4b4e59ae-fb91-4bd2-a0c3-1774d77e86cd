package worker

import (
	"checkurl/config"
	config2 "checkurl/internal/config"
	"checkurl/internal/db"
	"checkurl/internal/es"
	"checkurl/internal/mqqueue"
	"checkurl/internal/mqqueue/kafkamq"
	"checkurl/internal/url"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	sm "github.com/cch123/supermonkey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func InitRedisMock() *miniredis.Miniredis {
	// mock一个redis server
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	return s
}

func TestCheckurl(t *testing.T) {
	rMock := InitRedisMock()
	defer rMock.Close()
	assert.NotNil(t, rMock)

	rds := db.NewRedis(config.RedisConf{
		Addr:     rMock.Addr(),
		Password: "",
		Prefix:   "test:",
		Db:       12,
	})
	patch := sm.Patch((*es.Elastic6).Get, func(_ *es.Elastic6, id string) (m map[string]interface{}, err error) {
		return m, nil
	})
	defer patch.Unpatch()

	patch1 := sm.Patch((*FofaWorker).sendCrawlerTask, func(_ *FofaWorker, hostinfo *url.HostInfo, jarm string) {
		assert.Equal(t, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", jarm)
		assert.Equal(t, "https://feedback.hoobill.com", hostinfo.Url)
		assert.Equal(t, "https://feedback.hoobill.com", hostinfo.Host)
		assert.Equal(t, "feedback.hoobill.com", hostinfo.OnlyHost)
		assert.Equal(t, "hoobill.com", hostinfo.Domain)
		assert.Equal(t, "feedback", hostinfo.Subdomain)
	})
	patch2 := sm.Patch((*kafkamq.SimpleProducer).Publish, func(_ *kafkamq.SimpleProducer, topic string, data []byte) {
		fmt.Println("---------------", string(data))
	})
	defer patch2.Unpatch()

	worker := NewFofaWorker(&BaseWorker{
		crawlerProducer: &kafkamq.SimpleProducer{
			AsyncProducer: nil,
		},
		esStub: &es.Elastic6{},
		redis:  rds,
		config: config.Config{
			Producer: config.ProducerConfig{
				Crawler: mqqueue.SimplProducerConfig{
					Kafka: &config2.KafkaProducer{
						Topic: "test",
					},
				},
			},
			Worker: config.WorkerConf{
				AlexaDomainPath: "../../top-1m.csv",
			},
		},
	})

	mpDomainBlacklist := make(map[string]bool)
	worker.checkUrl("https://feedback.hoobill.com", false, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", mpDomainBlacklist)
	patch1.Unpatch()

	worker.checkUrl("https://fofa.so", false, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", mpDomainBlacklist)

}

func TestCheckurlERR(t *testing.T) {
	rMock := InitRedisMock()
	defer rMock.Close()
	assert.NotNil(t, rMock)

	rds := db.NewRedis(config.RedisConf{
		Addr:     rMock.Addr(),
		Password: "",
		Prefix:   "test:",
		Db:       12,
	})
	patch := sm.Patch((*es.Elastic6).Get, func(_ *es.Elastic6, id string) (m map[string]interface{}, err error) {
		return m, nil
	})
	defer patch.Unpatch()

	patch1 := sm.Patch((*FofaWorker).sendCrawlerTask, func(_ *FofaWorker, hostinfo *url.HostInfo, jarm string) {

	})
	patch2 := sm.Patch((*kafkamq.SimpleProducer).Publish, func(_ *kafkamq.SimpleProducer, topic string, data []byte) {
		fmt.Println("---------------", string(data))
	})
	defer patch2.Unpatch()

	worker := NewFofaWorker(&BaseWorker{
		crawlerProducer: &kafkamq.SimpleProducer{
			AsyncProducer: nil,
		},
		esStub: &es.Elastic6{},
		redis:  rds,
		config: config.Config{
			Producer: config.ProducerConfig{
				Crawler: mqqueue.SimplProducerConfig{
					Kafka: &config2.KafkaProducer{
						Topic: "test",
					},
				},
			},
			Worker: config.WorkerConf{
				AlexaDomainPath: "../../top-1m.csv",
			},
		},
	})

	mpDomainBlacklist := make(map[string]bool)
	//log.Println("[WARN] get host info failed", u, err)
	worker.checkUrl("ad*s123==", false, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", mpDomainBlacklist)
	//if !ip.Decimal(onlyhost) { // 但不是普通十进制形式视为非法(防止垃圾IP,单个IP用无限种字符串形式表示)
	worker.checkUrl("49w.60.1.196", false, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", mpDomainBlacklist)
	patch1.Unpatch()

	patch3 := sm.Patch((*db.Redis).IsBlackDomain, func(_ *db.Redis, domain string) bool {
		return true
	})

	//if !force && fw.redis.IsBlackDomain(hostinfo.Domain) && !fw.mpAlexa[hostinfo.Domain] { // 根域名黑名单
	worker.checkUrl("fofa.info", false, "2ad2ad0002ad2ad22c2ad2ad2ad2ad2eac92ec34bcc0cf7520e97547f83e81", mpDomainBlacklist)
	patch3.Unpatch()
}
