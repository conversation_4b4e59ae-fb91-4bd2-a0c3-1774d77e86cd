package ips

import (
	"bufio"
	"io"
	"strings"
)

var cdnmap = make(map[string]bool)

var cdnlist = `
// cloudflare
104.28.0 104.28.1 104.28.2 104.28.3 104.28.4 104.28.5 104.28.6 104.28.7 104.28.8 104.28.9 104.28.10 104.28.11 104.28.12 104.28.13 104.28.14 104.28.15 104.28.16 104.28.17 104.28.18 104.28.19 104.28.20 104.28.21 104.28.22 104.28.23 104.28.24 104.28.25 104.28.26 104.28.27 104.28.28 104.28.29 104.28.30 104.28.31

// unknown
116.31.127
`

func UpdateCdnString(list string) {
	rd := strings.NewReader(list)
	UpdateCdn(rd)
}

func UpdateCdn(rd io.Reader) {
	m := make(map[string]bool)
	r := bufio.NewReader(rd)
	for {
		line, _, err := r.<PERSON>Line()
		if err != nil {
			break
		}
		s := string(line)
		if strings.HasPrefix(s, "//") { // ignore comment
			continue
		}
		s = strings.TrimSpace(s)
		if len(s) == 0 { // ignore empty line
			continue
		}

		ss := strings.Split(s, " ")

		for _, prefix := range ss {
			prefix = strings.TrimSpace(prefix)
			if prefix == "" {
				continue
			}
			m[prefix] = true
		}
	}
}

func init() {
	UpdateCdnString(cdnlist)
}

func Cdn(ip string) bool {
	if i := strings.LastIndexByte(ip, '.'); i > 0 {
		ip = ip[:i]
	}
	if cdnmap[ip] {
		return true
	}
	return false
}
