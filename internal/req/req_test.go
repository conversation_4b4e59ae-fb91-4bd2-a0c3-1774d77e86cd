package req

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

func TestUrlParam(t *testing.T) {
	m := map[string]interface{}{
		"access_token": "123abc",
		"name":         "roc",
		"enc":          "中文",
	}
	queryHandler := func(w http.ResponseWriter, r *http.Request) {
		query := r.URL.Query()
		for key, value := range m {
			if v := query.Get(key); value != v {
				t.Errorf("query param %s = %s; want = %s", key, v, value)
			}
		}
	}
	ts := httptest.NewServer(http.HandlerFunc(queryHandler))
	_, err := Get(ts.URL, QueryParam(m))
	if err != nil {
		t.Fatal(err)
	}
	_, err = Head(ts.URL, Param(m))
	if err != nil {
		t.<PERSON>al(err)
	}
	_, err = Put(ts.URL, QueryParam(m))
	if err != nil {
		t.Fatal(err)
	}
}

/*func TestFormParam(t *testing.T) {
	formParam := Param{
		"access_token": "123abc",
		"name":         "roc",
		"enc":          "中文",
	}
	formHandler := func(w http.ResponseWriter, r *http.Request) {
		r.ParseForm()
		for key, value := range formParam {
			if v := r.FormValue(key); value != v {
				t.Errorf("form param %s = %s; want = %s", key, v, value)
			}
		}
	}
	ts := httptest.NewServer(http.HandlerFunc(formHandler))
	url := ts.URL
	_, err := Post(url, formParam)
	if err != nil {
		t.Fatal(err)
	}
}*/

func TestParamWithBody(t *testing.T) {
	reqBody := "request body"
	p := Param{
		"name": "roc",
		"job":  "programmer",
	}
	buf := bytes.NewBufferString(reqBody)
	ts := newDefaultTestServer()
	r, err := Post(ts.URL, p, buf)
	if err != nil {
		t.Fatal(err)
	}
	if r.Request().URL.Query().Get("name") != "roc" {
		t.Error("param should in the url when set body manually")
	}
	if string(r.reqBody) != reqBody {
		t.Error("request body not equal")
	}
}

func TestHeader(t *testing.T) {
	header := Header{
		"User-Agent":    "V1.0.0",
		"Authorization": "roc",
	}
	handler := func(w http.ResponseWriter, r *http.Request) {
		for key, value := range header {
			if v := r.Header.Get(key); value != v {
				t.Errorf("header %q = %s; want = %s", key, v, value)
			}
		}
	}
	ts := httptest.NewServer(http.HandlerFunc(handler))
	_, err := Head(ts.URL, header)
	if err != nil {
		t.Fatal(err)
	}

	httpHeader := make(http.Header)
	for key, value := range header {
		httpHeader.Add(key, value)
	}
	_, err = Head(ts.URL, httpHeader)
	if err != nil {
		t.Fatal(err)
	}
}

func TestUpload(t *testing.T) {
	str := "hello req"
	file := ioutil.NopCloser(strings.NewReader(str))
	upload := FileUpload{
		File:      file,
		FieldName: "media",
		FileName:  "hello.txt",
	}
	handler := func(w http.ResponseWriter, r *http.Request) {
		mr, err := r.MultipartReader()
		if err != nil {
			t.Fatal(err)
		}
		for {
			p, err := mr.NextPart()
			if err != nil {
				break
			}
			if p.FileName() != upload.FileName {
				t.Errorf("filename = %s; want = %s", p.FileName(), upload.FileName)
			}
			if p.FormName() != upload.FieldName {
				t.Errorf("formname = %s; want = %s", p.FileName(), upload.FileName)
			}
			data, err := ioutil.ReadAll(p)
			if err != nil {
				t.Fatal(err)
			}
			if string(data) != str {
				t.Errorf("file content = %s; want = %s", data, str)
			}
		}
	}
	ts := httptest.NewServer(http.HandlerFunc(handler))
	_, err := Post(ts.URL, upload)
	if err != nil {
		t.Fatal(err)
	}
	ts = newDefaultTestServer()
	_, err = Post(ts.URL, File("*.go"))
	if err != nil {
		t.Fatal(err)
	}
}

func Test_setBodyJson(t *testing.T) {
	type args struct {
		req  *http.Request
		resp *Resp
		opts *jsonEncOpts
		v    interface{}
	}
	tests := []struct {
		name    string
		args    args
		want    func()
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "pass",
			args: args{
				req: &http.Request{
					Header: map[string][]string{},
				},
				resp: &Resp{},
				opts: &jsonEncOpts{},
				v: struct {
				}{},
			},
		},
		{
			name: "pass",
			args: args{
				req: &http.Request{
					Header: map[string][]string{},
				},
				resp: &Resp{},
				opts: nil,
				v:    "1",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := setBodyJson(tt.args.req, tt.args.resp, tt.args.opts, tt.args.v)
			assert.NoError(t, err)
			//if !tt.wantErr(t, err, fmt.Sprintf("setBodyJson(%v, %v, %v, %v)", tt.args.req, tt.args.resp, tt.args.opts, tt.args.v)) {
			//	return
			//}
			//assert.Equalf(t, tt.want, got, "setBodyJson(%v, %v, %v, %v)", tt.args.req, tt.args.resp, tt.args.opts, tt.args.v)
		})
	}
}
