package repository

import (
	"bytes"
	"encoding/json"
	"git.gobies.org/shared-platform/foscan/pkg/model"
	"github.com/pkg/errors"
	"go-micro.dev/v4/logger"
	"go-micro.dev/v4/store"
)

func (repo *repository) AddProtocols(protocols []model.Protocol) error {
	var buf bytes.Buffer

	for _, p := range protocols {
		err := json.NewEncoder(&buf).Encode(p)

		if err != nil {
			logger.Warnf("repository.addProtocols encode protocol failed %v", p)
			continue
		}

		err = repo.protocolStore.Write(&store.Record{
			Key:   p.ID,
			Value: buf.Bytes(),
		})

		if err != nil {
			return err
		}
	}

	return nil
}

func (repo *repository) UpdateProtocol(p model.Protocol) error {
	var buf bytes.Buffer

	err := json.NewEncoder(&buf).Encode(p)

	if err != nil {
		return errors.Wrapf(err, "repository.UpdateProtocol encode protocol failed %v", p)
	}

	return repo.protocolStore.Write(&store.Record{
		Key:   p.ID,
		Value: buf.Bytes(),
	})
}

func (repo *repository) DeleteProtocol(ids []string, isAll bool) error {
	var err error

	if isAll {
		keys, err := repo.protocolStore.List()

		if err != nil {
			return err
		}

		for _, k := range keys {
			err = repo.protocolStore.Delete(k)
		}

		return err
	}

	for _, id := range ids {
		err = repo.protocolStore.Delete(id)
	}

	return err
}

func (repo *repository) AllProtocols() ([]model.Protocol, error) {
	keys, err := repo.protocolStore.List()

	if err != nil {
		return []model.Protocol{}, err
	}

	var protocols []model.Protocol

	for _, key := range keys {
		records, err := repo.protocolStore.Read(key)
		if err != nil {
			logger.Warnf("repository.AllRules Read error key: %s", key)
			continue
		}

		for _, record := range records {
			var protocol model.Protocol
			err = json.Unmarshal(record.Value, &protocol)

			if err != nil {
				logger.Warnf("repository.AllRules json.Unmarshal record to Rule error record: %s", string(record.Value))
				continue
			}

			protocols = append(protocols, protocol)
		}
	}

	return protocols, nil
}
