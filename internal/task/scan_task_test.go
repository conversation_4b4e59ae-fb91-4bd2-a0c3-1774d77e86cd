package task

import (
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestNewScantask(t *testing.T) {
	stbc := NewScantaskBaseConf(ETMasscan, "", "host-1", "", 48914, 30, 100,
		false, true, false)
	stbc.blackipList = "127.0.0.1"
	blackIps := stbc.MergeExcludeIps("*********/10")
	assert.Equal(t, "***************,*********/10,127.0.0.1", blackIps)

	stbc.typ = ETNmap
	nst := NewScantask(stbc)
	assert.NotNil(t, nst)

	assert.True(t, stbc.IpIsInner("127.0.0.1"))
	assert.True(t, stbc.IpIsInner("***********"))
	assert.True(t, stbc.IpIsInner("************"))
	assert.False(t, stbc.IpIsInner("2406:da18:880:3800:3cf7:d90b:9468:f4a6"))
	assert.False(t, stbc.IpIsInner("************"))

	filename := "not_exists_file_lz_2022.txt.json"
	assert.False(t, stbc.FileExists(filename))

	curDir, _ := os.Getwd()
	filename = curDir + "/scan_task.go"
	assert.True(t, stbc.FileExists(filename))

	stbc.typ = 10
	nst = NewScantask(stbc)
	assert.Nil(t, nst)

	stbc.typ = ETTreckScan
	nst = NewScantask(stbc)
	assert.NotNil(t, nst)
}
