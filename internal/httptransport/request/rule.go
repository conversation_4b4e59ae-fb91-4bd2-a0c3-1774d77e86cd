package request

type AddRule struct {
	Product          string `json:"product" binding:"required"`
	ENProduct        string `json:"en_product"`
	Rule             string `json:"rule" binding:"required"`
	RuleID           string `json:"rule_id" binding:"required"`
	Level            int    `json:"level" binding:"oneof=0 1 2 3 4 5"`
	Category         string `json:"category" binding:"required"`
	ENCategory       string `json:"en_category"`
	ParentCategory   string `json:"parent_category" binding:"required"`
	ENParentCategory string `json:"en_parent_category"`
	Company          string `json:"company" binding:"required"`
	ENCompany        string `json:"en_company"`
	From             string `json:"from"`
	UserID           string `json:"user_id"`
}

type UpdateRule struct {
	Product          string `json:"product" binding:"required"`
	ENProduct        string `json:"en_product"`
	Rule             string `json:"rule" binding:"required"`
	Level            int    `json:"level" binding:"oneof=0 1 2 3 4 5"`
	Company          string `json:"company" binding:"required"`
	ENCompany        string `json:"en_company"`
	Category         string `json:"category" binding:"required"`
	ENCategory       string `json:"en_category"`
	ParentCategory   string `json:"parent_category" binding:"required"`
	ENParentCategory string `json:"en_parent_category"`
	From             string `json:"from"`
	UserID           string `json:"user_id"`
}
