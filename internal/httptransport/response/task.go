package response

type DeleteTaskResponse struct {
	TaskID string `json:"task_id" binding:"required"`
}

type PauseTaskResponse struct {
	// 任务 ID
	//
	// Required: true
	TaskID string `json:"task_id"`

	// 任务状态
	//
	// Required: false
	// Example: waiting; running; paused; failed; success
	Status string `json:"status"`

	// 任务消息
	Message string `json:"message"`

	// 任务进度
	// Example: 78.99
	Progress float64 `json:"progress"`
}

type ResumeTaskResponse struct {
	// 任务 ID
	//
	// Required: true
	TaskID string `json:"task_id"`

	// 任务状态
	//
	// Required: false
	// Example: waiting; running; paused; failed; success
	Status string `json:"status"`
}

type GetTaskResponse struct {
	// 任务 ID
	//
	// Required: true
	TaskID string `json:"task_id"`

	// 任务状态
	//
	// Required: false
	// Example: waiting; running; paused; failed; success
	Status string `json:"status"`

	Progress  float32 `json:"progress"`
	WorkingOn string  `json:"working_on"`
}

type AddTaskResponse struct {
	FailedDomains    []string            `json:"failed_domains"`
	IpDomainRelation map[string][]string `json:"ip_domain_relation"`
}
