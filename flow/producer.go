package flow

import (
	"baimaohui/portscan_new/internal/config"
	"baimaohui/portscan_new/internal/queue"
	"baimaohui/portscan_new/internal/queue/kafkaq"
	"log"
)

func NewProducer(conf config.ProducerConfig) queue.SimpleProducer {
	if conf.Kafka != nil {
		return NewKafkaProducer(conf.Kafka)
	}

	log.Fatal("please give a producer")
	return nil
}

func NewKafkaProducer(conf *config.KafkaProducer) queue.SimpleProducer {
	if conf == nil {
		log.Println("[FATAL] kafka config is nil")
		return nil
	}

	sp, err := kafkaq.NewCommonSimpleProducer(conf.Brokers, false, false, handleError, nil)
	if err != nil {
		log.Fatal(err)
	}

	return sp
}
