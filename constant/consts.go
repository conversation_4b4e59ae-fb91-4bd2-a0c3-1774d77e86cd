package constant

const (
	DefaultVersion = "1.0"

	PrefixService = "net.baimaohui.srv.foscan"

	ServiceApi          = PrefixService + "." + "api"
	ServiceDispatcher   = PrefixService + "." + "dispatcher"
	ServicePortScanner  = PrefixService + "." + "port_scanner"
	ServiceQuickStore   = PrefixService + "." + "quick_store"
	ServiceRawGrab      = PrefixService + "." + "raw_grab"
	ServiceCheckUrl     = PrefixService + "." + "check_url"
	ServiceCrawler      = PrefixService + "." + "crawler"
	ServiceDataAnalysis = PrefixService + "." + "data_analysis"
)

const (
	StateInitial     = "Initial"
	StateDispatching = "Dispatching"
	StateStarting    = "Starting"
	StateRunning     = "Running"
	StateStopping    = "Stopping"
	StateStopped     = "Stopped"
	StatePausing     = "Pausing"
	StatePaused      = "Paused"
	StateResuming    = "Resuming"
	StateError       = "Error"
	StateFinished    = "Finished"
)

const (
	CommandStart  = "Start"
	CommandPause  = "Pause"
	CommandResume = "Resume"
	CommandStop   = "Stop"
)

// TaskType is used to task type field from string transfer to uint.
var TaskType = map[string]uint{
	"quick":  TaskTypeQuick,
	"common": TaskTypeCommon,
	"ping":   TaskTypePing,
}

const (
	TaskTypeQuick = iota
	TaskTypeCommon
	TaskTypePing
)
