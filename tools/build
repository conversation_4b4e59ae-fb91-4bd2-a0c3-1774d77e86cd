#! /bin/bash

set -eux

APP="portscan"
IMAGE=$APP
TAG=$(cat VERSION)
DIR=$(dirname $(dirname $PWD))

# 编译程序
docker run --rm \
	-v $DIR:/go/src/backend \
    -w /go/src/backend/fofa/$APP \
	golang:1.9.1-stretch \
    go build

# 打包镜像
tag=docker.fofa.so/library/${IMAGE}:${TAG}
tag_latest=docker.fofa.so/library/${IMAGE}:latest
docker build . -t ${tag}
docker tag ${tag} ${tag_latest}

# 删除程序
rm $APP

# 上传
docker push ${tag}
docker push ${tag_latest}

