package main

import (
	"github.com/gin-gonic/gin"
	"log"
	"net/http"
)

const (
	StatusStarting = "1"
	StatusRunning  = "2"
	StatusStopping = "3"
	StatusStopped  = "4"
	StatusSuccess  = "5"
	StatusFailed   = "6"
	StatusPaused   = "7"
)

var taskRecord = make(map[string]string)

func main() {
	r := gin.New()
	r.GET("/api/v1/callbacks/asset_scan_progress", progressCallback)
	r.GET("/api/v1/callbacks/asset_scan_state", stateCallback)
	r.GET("/api/v1/tasks/:task_id/state", queryStateByTaskID)

	log.Printf("http.ListenAndServe ON: %d", 6789)
	log.Fatal(http.ListenAndServe(":6789", r))
}

func progressCallback(ctx *gin.Context) {
	taskID := ctx.Query("task_id")
	state := ctx.Query("state")

	if len(taskID) <= 0 || len(state) <= 0 {
		ctx.JSON(http.StatusOK, gin.H{
			"message": "通知进度错误：回调数据没有task_id或者state",
		})
		return
	}

	taskRecord[taskID] = state

	ctx.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
	return
}

func stateCallback(ctx *gin.Context) {
	taskID := ctx.Query("task_id")
	state := ctx.Query("state")

	if len(taskID) <= 0 || len(state) <= 0 {
		ctx.JSON(http.StatusOK, gin.H{
			"message": "通知状态错误：回调数据没有task_id或者state",
		})
		return
	}

	taskRecord[taskID] = state

	ctx.JSON(http.StatusOK, gin.H{
		"message": "success",
	})
	return
}

func queryStateByTaskID(ctx *gin.Context) {
	taskID := ctx.Param("task_id")

	if len(taskID) <= 0 {
		ctx.JSON(http.StatusUnprocessableEntity, gin.H{
			"message": "查询错误：task_id不存在",
		})
		return
	}

	state, ok := taskRecord[taskID]

	if !ok {
		ctx.JSON(http.StatusUnprocessableEntity, gin.H{
			"message": "任务不存在",
		})
		return
	}

	if state == StatusStopped ||
		state == StatusSuccess ||
		state == StatusFailed {
		delete(taskRecord, taskID)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": "",
		"data": gin.H{
			"state": state,
		},
	})
	return
}
