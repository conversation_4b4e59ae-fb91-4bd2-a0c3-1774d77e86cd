package company_icp

import (
	"database/sql"
	"errors"
	"fmt"
	core "micro-service/coreService/proto"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type (
	CompanyIcpModel interface {
		Create(info *CompanyIcp) error
		First(opts ...mysql.HandleFunc) (CompanyIcp, error)
		List(page, size int, opts ...mysql.HandleFunc) (list []CompanyIcp, total int64, err error)
		ListAll(opts ...mysql.HandleFunc) (list []*CompanyIcp, err error)
		UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
		Upsert(list ...*CompanyIcp) error
		GetById(id uint64, getEquals bool) ([]CompanyIcp, error)
		GetByName(companyName string, getEquals bool) ([]CompanyIcp, error)
		GetByDomain(domain string, getEquals bool) ([]CompanyIcp, error)
		GetByIcp(icp string, getEquals bool) ([]CompanyIcp, error)
		CreateOrUpdateMainInfo(an *core.BeiAn, source string) (*CompanyIcp, error)
		CreateOrUpdateInfo(companyName string, mainInfo *CompanyIcp, beiAns []*core.BeiAn, source string) error
		UpdateStatus(companyIcps []CompanyIcp, status int) error
		Updates(item CompanyIcp, opts ...mysql.HandleFunc) error
		DeleteByIds(ids []uint64) error
		FindOnlineByIcp(icp string) (*CompanyIcp, []CompanyIcp, error)
		FindOnlineByCompanyName(companyName string) (*CompanyIcp, []CompanyIcp, error)
		FindOnlineByDomain(domain string) (*CompanyIcp, []CompanyIcp, error)
		SetOfflineByIcp(icp string) error
		SetOfflineByIcpAndName(icp, name string) error
		SetOfflineByCompanyName(companyName string) error
		SetOfflineByDomain(domain string) error
		SetOfflineBy(icp, companyName string) error
	}

	defaultCompanyIcpModel struct {
		*gorm.DB
		table string
	}

	CompanyIcp struct {
		dbx.Model
		Name       string       `gorm:"column:name;type:varchar(300);comment:企业名称;NOT NULL" json:"name"`
		Icp        string       `gorm:"column:icp;type:varchar(255);comment:备案号;DEFAULT NULL" json:"icp"`
		ParentId   uint64       `gorm:"column:parent_id;type:bigint(20) unsigned;default:0;comment:父级ID;NOT NULL" json:"parent_id"`
		Type       int8         `gorm:"column:type;type:tinyint(4);comment:企业类型1/2 企业 /个人;default:0;NOT NULL" json:"type"`
		Domain     string       `gorm:"column:domain;type:varchar(255);comment:企业域名;DEFAULT NULL" json:"domain"`
		RecordTime sql.NullTime `gorm:"column:record_time;default:null" json:"record_time"` // 备案时间
		Source     string       `gorm:"column:source"`                                      // 备案信息来源
		Status     int          `gorm:"column:status"`                                      // 备案状态
	}
)

const CompanyIcpTable = "company_icp"

const (
	IcpStatusOnline  = iota + 1 // 正常
	IcpStatusOffline            // 注销
)

const (
	SourceMIIT   = "miit"
	SourceChinaz = "chinaz_api"
	SourceImport = "import"
)

// TableName 表名
func (c *CompanyIcp) TableName() string {
	return CompanyIcpTable
}

func GetTypeString(icpType int8) string {
	m := map[int8]string{
		1: "企业", 2: "个人", 3: "政府机关", 4: "事业单位",
		5: "社会团体", 6: "国防机构", 7: "民办非企业单位", 8: "基金会",
		9: "律师执业机构", 10: "外国在华文化中心", 11: "群众性团体组织", 12: "司法鉴定机构",
		13: "宗教团体", 14: "境外机构", 15: "医疗机构", 16: "公证机构",
	}
	return m[icpType]
}
func GetTypeInt(icpType string) int8 {
	m := map[string]int8{
		"企业": 1, "个人": 2, "政府机关": 3, "事业单位": 4, "社会团体": 5,
		"国防机构": 6, "民办非企业单位": 7, "基金会": 8, "律师执业机构": 9, "外国在华文化中心": 10,
		"群众性团体组织": 11, "司法鉴定机构": 12, "宗教团体": 13, "境外机构": 14, "医疗机构": 15, "公证机构": 16,
	}
	if val, ok := m[icpType]; !ok {
		return 1
	} else {
		return val
	}
}

func NewCompanyIcpModel(conn ...*gorm.DB) CompanyIcpModel {
	return &defaultCompanyIcpModel{mysql.GetDbClient(conn...), CompanyIcpTable}
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return func(db *gorm.DB) {
		if keyword != "" {
			keyword = "%" + keyword + "%"
			db.Where("`name` LIKE ? OR `icp` LIKE ? OR `domain` LIKE ?", keyword, keyword, keyword)
		}
	}
}

func (d *defaultCompanyIcpModel) Create(info *CompanyIcp) error {
	return d.DB.Model(&CompanyIcp{}).Create(info).Error
}

func (d *defaultCompanyIcpModel) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&CompanyIcp{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *defaultCompanyIcpModel) Upsert(list ...*CompanyIcp) error {
	if len(list) == 0 {
		return nil
	}
	return d.DB.Save(list).Error
}

// FindOnlineByCompanyName 根据企业名称获取正常备案
func (d *defaultCompanyIcpModel) FindOnlineByCompanyName(companyName string) (*CompanyIcp, []CompanyIcp, error) {
	var mainIcp CompanyIcp
	var companyIcps []CompanyIcp
	// 查找备案主体
	if err := d.DB.Model(&CompanyIcp{}).Where("name = ? and parent_id = 0 and status = 1", companyName).Limit(1).First(&mainIcp).Error; err != nil {
		return nil, companyIcps, err
	}
	// 查找备案信息
	if err := d.DB.Model(&CompanyIcp{}).Where("name = ? and parent_id != 0 and status = 1", companyName).Find(&companyIcps).Error; err != nil {
		return &mainIcp, companyIcps, err
	}
	return &mainIcp, companyIcps, nil
}

// FindOnlineByDomain 根据域名获取正常备案
func (d *defaultCompanyIcpModel) FindOnlineByDomain(domain string) (*CompanyIcp, []CompanyIcp, error) {
	var tmp CompanyIcp
	if err := d.DB.Model(&CompanyIcp{}).Where("domain = ? and status = 1", domain).Limit(1).First(&tmp).Error; err != nil {
		return nil, nil, err
	}
	if tmp.Type == 2 {
		return d.FindOnlineByIcp(utils.RemoveIcpNumber(tmp.Icp))
	} else {
		return d.FindOnlineByCompanyName(tmp.Name)
	}
}

// FindOnlineByIcp 根据ICP获取正常备案
func (d *defaultCompanyIcpModel) FindOnlineByIcp(icp string) (*CompanyIcp, []CompanyIcp, error) {
	icp = utils.RemoveIcpNumber(icp)
	var mainIcp CompanyIcp
	var companyIcps []CompanyIcp
	// 查找备案主体
	if err := d.DB.Model(&CompanyIcp{}).Where(fmt.Sprintf(`icp = "%s" and parent_id = 0 and status = 1`, icp)).Limit(1).First(&mainIcp).Error; err != nil {
		return nil, companyIcps, err
	}
	// 查找备案信息
	if err := d.DB.Model(&CompanyIcp{}).Where(fmt.Sprintf(`icp like "%s%%" and parent_id != 0 and status = 1`, icp)).Find(&companyIcps).Error; err != nil {
		return &mainIcp, companyIcps, err
	}
	return &mainIcp, companyIcps, nil
}

// SetOfflineByCompanyName 根据企业名称设置ICP已注销
func (d *defaultCompanyIcpModel) SetOfflineByCompanyName(companyName string) error {
	if companyName != "" {
		return d.DB.Model(&CompanyIcp{}).Where("name = ?", companyName).
			Where("status = ?", IcpStatusOnline).Update("status", IcpStatusOffline).Error
	}
	return nil
}

// SetOfflineByIcp 根据Icp号设置ICP已注销
func (d *defaultCompanyIcpModel) SetOfflineByIcp(icp string) error {
	if icp != "" {
		return d.DB.Model(&CompanyIcp{}).Where("icp like ?", icp+"%").
			Where("status = ?", IcpStatusOnline).Update("status", IcpStatusOffline).Error
	}
	return nil
}

// SetOfflineByIcpAndName 根据Icp号,并且名称不匹配的设置ICP已注销
func (d *defaultCompanyIcpModel) SetOfflineByIcpAndName(icp, name string) error {
	if icp != "" && name != "" {
		return d.DB.Model(&CompanyIcp{}).Where("icp like ? and name != ?", icp+"%", name).
			Where("status = ?", IcpStatusOnline).Update("status", IcpStatusOffline).Error
	}
	return nil
}

// SetOfflineByDomain 根据域名设置ICP已注销
func (d *defaultCompanyIcpModel) SetOfflineByDomain(domain string) error {
	if domain != "" {
		return d.DB.Model(&CompanyIcp{}).Where("domain = ?", domain).
			Where("status = ?", IcpStatusOnline).Update("status", IcpStatusOffline).Error
	}
	return nil
}

// SetOfflineBy 设置ICP已注销
func (d *defaultCompanyIcpModel) SetOfflineBy(icp, companyName string) error {
	if icp != "" && companyName != "" {
		// 注销ICP不一致的,企业备案
		if err := d.DB.Model(&CompanyIcp{}).Where("icp like ? and name != ?", icp+"%", companyName).Update("status", IcpStatusOffline).Error; err != nil {
			return err
		}
		// 注销企业不一致的,ICP备案
		if err := d.DB.Model(&CompanyIcp{}).Where("icp not like ? and name = ?", icp+"%", companyName).Update("status", IcpStatusOffline).Error; err != nil {
			return err
		}
	}
	return nil
}

func (d *defaultCompanyIcpModel) First(opts ...mysql.HandleFunc) (CompanyIcp, error) {
	query := d.DB.Model(&CompanyIcp{})
	for _, opt := range opts {
		opt(query)
	}

	var companyIcp CompanyIcp
	err := query.First(&companyIcp).Error
	return companyIcp, err
}

func (d *defaultCompanyIcpModel) List(page, size int, opts ...mysql.HandleFunc) ([]CompanyIcp, int64, error) {
	query := d.DB.Model(&CompanyIcp{})
	for _, opt := range opts {
		opt(query)
	}

	var companyIcps []CompanyIcp
	var total int64
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&companyIcps).Error
	if err != nil {
		return nil, 0, err
	}
	return companyIcps, total, nil
}

func (d *defaultCompanyIcpModel) ListAll(opts ...mysql.HandleFunc) ([]*CompanyIcp, error) {
	query := d.DB.Model(&CompanyIcp{})
	for _, opt := range opts {
		opt(query)
	}

	var companyIcps []*CompanyIcp
	err := query.Find(&companyIcps).Error
	if err != nil {
		return nil, err
	}
	return companyIcps, nil
}

// GetById 根据ID获取备案信息
func (d *defaultCompanyIcpModel) GetById(id uint64, getEquals bool) ([]CompanyIcp, error) {
	if id == 0 {
		return nil, errors.New("CompanyRecord is Empty")
	}
	var companyIcps []CompanyIcp
	if getEquals {
		err := d.DB.Table(d.table).Where("id = ? or  parent_id  = ?", id, id).Order("parent_id asc").Find(&companyIcps).Error
		return companyIcps, err
	} else {
		err := d.DB.Table(d.table).Where("id", id).Find(&companyIcps).Error
		return companyIcps, err
	}
}

// GetByName 根据企业名称获取备案信息
func (d *defaultCompanyIcpModel) GetByName(companyName string, getEquals bool) ([]CompanyIcp, error) {
	var companyIcps []CompanyIcp
	if getEquals {
		err := d.DB.Table(d.table).Where("(name = ?)  and status = 1", companyName).Order("parent_id asc").Find(&companyIcps).Error
		return companyIcps, err
	} else {
		err := d.DB.Table(d.table).Where("(name = ? and parent_id = 0)  and status = 1", companyName).Find(&companyIcps).Error
		return companyIcps, err
	}
}

// GetByDomain 根据域名获取备案信息
func (d *defaultCompanyIcpModel) GetByDomain(domain string, getEquals bool) ([]CompanyIcp, error) {
	var companyIcps []CompanyIcp
	var companyIcp CompanyIcp
	mainInfoErr := d.DB.Table(d.table).Where("domain = ? and status = 1", domain).First(&companyIcp).Error
	if mainInfoErr != nil {
		return nil, mainInfoErr
	}
	if getEquals {
		if companyIcp.ParentId != 0 {
			err := d.DB.Table(d.table).Where("(id = ? or parent_id = ?) and status = 1", companyIcp.ParentId, companyIcp.ParentId).Order("parent_id asc").Find(&companyIcps).Error
			return companyIcps, err
		} else {
			err := d.DB.Table(d.table).Where("(id = ? or parent_id = ?) and status = 1", companyIcp.Id, companyIcp.Id).Order("parent_id asc").Find(&companyIcps).Error
			return companyIcps, err
		}
	} else {
		companyIcps = append(companyIcps, companyIcp)
		return companyIcps, nil
	}
}

// GetByIcp 根据ICP获取备案信息
func (d *defaultCompanyIcpModel) GetByIcp(icp string, getEquals bool) ([]CompanyIcp, error) {
	var companyIcps []CompanyIcp
	icp = utils.RemoveIcpNumber(icp)
	if getEquals {
		err := d.DB.Table(d.table).Where("(icp like ?) and status = 1", icp+"%").Order("parent_id asc").Find(&companyIcps).Error
		println(fmt.Sprintf("%v", companyIcps))
		return companyIcps, err
	} else {
		err := d.DB.Table(d.table).Where("(icp = ? and parent_id = 0)  and status = 1", icp).Order("updated_at desc").Limit(1).Find(&companyIcps).Error
		return companyIcps, err
	}
}

// CreateOrUpdateMainInfo 创建或更新主体信息
func (d *defaultCompanyIcpModel) CreateOrUpdateMainInfo(an *core.BeiAn, source string) (*CompanyIcp, error) {
	var icp CompanyIcp
	// 查找数据
	if an == nil {
		return nil, errors.New("备案信息为空")
	}
	icpStr := utils.RemoveIcpNumber(an.Icp)
	if err := d.DB.Table(d.table).Where("parent_id = ? and icp = ?", 0, icpStr).Find(&icp).Error; err != nil {
		return nil, err
	}
	if icp.Id != 0 { // 数据存在
		if res := d.DB.Table(d.table).Where("id", icp.Id).Update("icp", icpStr); res.Error != nil {
			return nil, res.Error
		}
	} else { // 数据不存在
		icp.Name = an.CompanyName
		icp.Icp = utils.RemoveIcpNumber(an.Icp)
		icp.Type = GetTypeInt(an.CompanyType)
		icp.Source = source
		icp.Status = IcpStatusOnline
		if res := d.DB.Table(d.table).Create(&icp); res.Error != nil {
			return nil, res.Error
		}
	}
	return &icp, nil
}

// CreateOrUpdateInfo  创建或更新备案信息
func (d *defaultCompanyIcpModel) CreateOrUpdateInfo(companyName string, mainInfo *CompanyIcp, beiAns []*core.BeiAn, source string) error {
	var findIds []string
	for _, beiAn := range beiAns {
		var icp CompanyIcp
		err := d.DB.Model(&CompanyIcp{}).Where("`name` = ? AND `parent_id` = ?", companyName, mainInfo.Id).
			Where("`icp` = ? AND `domain` = ?", beiAn.Icp, beiAn.WebsiteUrl).Find(&icp).Error
		if err != nil {
			return err
		}
		icp.Icp = beiAn.Icp
		icp.Type = GetTypeInt(beiAn.CompanyType)
		icp.Name = companyName
		icp.Source = source
		parsedAuditTime := utils.TimeStringToGoTime(beiAn.AuditTime)
		if !parsedAuditTime.IsZero() {
			icp.RecordTime = sql.NullTime{Time: parsedAuditTime, Valid: true}
		} else {
			icp.RecordTime = sql.NullTime{Valid: true, Time: time.Now()}
		}
		icp.Domain = beiAn.WebsiteUrl
		icp.ParentId = mainInfo.Id
		if icp.Status == 0 {
			icp.Status = IcpStatusOnline
		}
		if icp.Id != 0 { // 数据存在
			findIds = append(findIds, cast.ToString(icp.Id))
			if res := d.DB.Model(&CompanyIcp{}).Select([]string{"name", "parent_id", "type", "icp", "domain", "record_time", "status", "source", "created_at", "updated_at"}).
				Where("`id` = ?", icp.Id).Updates(icp); res.Error != nil {
				return res.Error
			}
		} else { // 数据不存在
			if res := d.DB.Model(&CompanyIcp{}).Create(&icp); res.Error != nil {
				return res.Error
			}
		}
	}
	if len(findIds) != 0 {
		// 未查到ICP时,更新ICP状态为注销状态
		d.DB.Model(&CompanyIcp{}).
			Where("`name` = ? AND `parent_id` != 0 AND `id` NOT IN (?)", companyName, findIds).Update("status", IcpStatusOffline)
	}
	return nil
}

func (d *defaultCompanyIcpModel) Updates(item CompanyIcp, opts ...mysql.HandleFunc) error {
	q := d.DB.Table(d.table)
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(item).Error
}

func (d *defaultCompanyIcpModel) UpdateStatus(companyIcps []CompanyIcp, status int) error {
	var ids []string
	for _, icp := range companyIcps {
		ids = append(ids, cast.ToString(icp.Id))
	}
	return d.DB.Table(d.table).Where(fmt.Sprintf("id in (%s)", strings.Join(ids, ","))).Update("status", status).Error
}

func (d *defaultCompanyIcpModel) DeleteByIds(ids []uint64) error {
	if len(ids) == 0 {
		return nil
	}
	return d.DB.Where("id IN (?)", ids).Delete(&CompanyIcp{}).Error
}
