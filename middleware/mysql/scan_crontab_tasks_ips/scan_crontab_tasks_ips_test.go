package scan_crontab_tasks_ips

import (
	"database/sql"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"

	initmysql "micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dbx"
)

func init() {
	cfg.InitLoadCfg()
	// 设置测试环境标志
	initmysql.SetTestEnv(true)

	// 初始化数据库连接
	initmysql.GetInstance(cfg.LoadMysql())
}

func TestNewModel(t *testing.T) {
	model := NewModel()
	assert.NotNil(t, model)
}

func TestDefaultScanCrontabTasksIpModel_FindByCronTaskId(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(123)
		expectedIps := []*ScanCrontabTasksIp{
			{
				Model:      dbx.Model{Id: 1},
				CronTaskId: cronTaskId,
				Ip:         "***********",
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			},
			{
				Model:      dbx.Model{Id: 2},
				CronTaskId: cronTaskId,
				Ip:         "***********",
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			},
		}

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "ip", "created_at", "updated_at",
		})
		for _, ip := range expectedIps {
			rows.AddRow(
				ip.Id, ip.CronTaskId, ip.Ip, ip.CreatedAt, ip.UpdatedAt,
			)
		}

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedIps[0].Ip, result[0].Ip)
		assert.Equal(t, expectedIps[1].Ip, result[1].Ip)
		assert.Equal(t, cronTaskId, result[0].CronTaskId)
		assert.Equal(t, cronTaskId, result[1].CronTaskId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("failure", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(123)

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnError(sql.ErrConnDone)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.Error(t, err)
		assert.Nil(t, result)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("empty_result", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(999)

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "ip", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试常量
func TestConstants(t *testing.T) {
	assert.Equal(t, "cron_task_ips", TableName)
}

// 测试结构体字段
func TestScanCrontabTasksIp_Fields(t *testing.T) {
	now := time.Now()
	ip := &ScanCrontabTasksIp{
		Model: dbx.Model{
			Id:        1,
			CreatedAt: now,
			UpdatedAt: now,
		},
		CronTaskId: 123,
		Ip:         "***********00",
		CreatedAt:  now,
		UpdatedAt:  now,
	}

	assert.Equal(t, uint64(1), ip.Id)
	assert.Equal(t, uint64(123), ip.CronTaskId)
	assert.Equal(t, "***********00", ip.Ip)
	assert.NotNil(t, ip.CreatedAt)
	assert.NotNil(t, ip.UpdatedAt)
}

// 测试边界情况
func TestScanCrontabTasksIp_EdgeCases(t *testing.T) {
	t.Run("zero_cron_task_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(0)

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "ip", "created_at", "updated_at",
		})

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 0)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("large_cron_task_id", func(t *testing.T) {
		mock := initmysql.GetMockInstance()
		model := NewModel()

		cronTaskId := uint64(9223372036854775807) // 使用int64最大值，避免高位设置的uint64

		rows := sqlmock.NewRows([]string{
			"id", "cron_task_id", "ip", "created_at", "updated_at",
		}).AddRow(1, cronTaskId, "********", time.Now(), time.Now())

		mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
			WithArgs(cronTaskId).
			WillReturnRows(rows)

		result, err := model.FindByCronTaskId(cronTaskId)
		assert.NoError(t, err)
		assert.Len(t, result, 1)
		assert.Equal(t, cronTaskId, result[0].CronTaskId)
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

// 测试不同IP格式
func TestScanCrontabTasksIp_DifferentIpFormats(t *testing.T) {
	mock := initmysql.GetMockInstance()
	model := NewModel()

	testCases := []struct {
		name string
		ip   string
	}{
		{
			name: "ipv4_address",
			ip:   "***********",
		},
		{
			name: "ipv6_address",
			ip:   "2001:0db8:85a3:0000:0000:8a2e:0370:7334",
		},
		{
			name: "localhost",
			ip:   "127.0.0.1",
		},
		{
			name: "empty_ip",
			ip:   "",
		},
		{
			name: "ip_range",
			ip:   "***********/24",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			cronTaskId := uint64(123)

			rows := sqlmock.NewRows([]string{
				"id", "cron_task_id", "ip", "created_at", "updated_at",
			}).AddRow(1, cronTaskId, tc.ip, time.Now(), time.Now())

			mock.ExpectQuery("SELECT (.+) FROM `cron_task_ips` WHERE cron_task_id = (.+)").
				WithArgs(cronTaskId).
				WillReturnRows(rows)

			result, err := model.FindByCronTaskId(cronTaskId)
			assert.NoError(t, err)
			assert.Len(t, result, 1)
			assert.Equal(t, tc.ip, result[0].Ip)
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

// 测试模型接口实现
func TestScanCrontabTasksIpModelInterface(t *testing.T) {
	model := NewModel()
	
	// 确保实现了ScanCrontabTasksIpModel接口
	var _ ScanCrontabTasksIpModel = model
	
	// 测试接口方法存在
	assert.NotNil(t, model)
}

// 测试默认模型结构
func TestDefaultScanCrontabTasksIpModel_Structure(t *testing.T) {
	model := NewModel()
	
	// 类型断言确保返回的是正确的类型
	defaultModel, ok := model.(*defaultScanCrontabTasksIpModel)
	assert.True(t, ok)
	assert.Equal(t, TableName, defaultModel.table)
	assert.NotNil(t, defaultModel.DB)
}
