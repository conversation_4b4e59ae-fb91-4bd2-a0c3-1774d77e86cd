package intelligence

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"time"

	"gorm.io/gorm"
)

type IntelligenceOther interface {
	Create(*Other) error
	First(...mysql.HandleFunc) (Other, error)
	Count(opts ...mysql.HandleFunc) (int64, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]Other, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*Other, error)
	Update(Other) error
	UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error
	Save([]*Other) error
	DeleteById(uint64) error
	DeleteByIds(id []uint64) error
}

const (
	IsPublicTrue  = 1
	IsPublicFalse = 2
)

func NewOther(db ...*gorm.DB) IntelligenceOther {
	return &defaultOther{DB: mysql.GetDbClient(db...)}
}

// Other 线索表
type Other struct {
	dbx.Model
	Url              string    `gorm:"column:url;comment:URL" json:"url"`
	Platform         string    `gorm:"column:platform;comment:平台" json:"platform"`
	Keyword          string    `gorm:"column:keyword;comment:关键词" json:"keyword"`
	Poster           string    `gorm:"column:poster;comment:发帖人" json:"poster"`
	Title            string    `gorm:"column:title;comment:标题" json:"title"`
	Sample           string    `gorm:"column:sample;comment:样本" json:"sample"`
	Screenshot       string    `gorm:"column:screenshot;comment:截图" json:"screenshot"`
	ArticleId        string    `gorm:"column:article_id;comment:文章ID" json:"article_id"`
	ArticleContext   string    `gorm:"column:article_context;comment:文章内容" json:"article_context"`
	ArticleCreatedAt time.Time `gorm:"column:article_created_at;comment:收录时间" json:"article_created_at"`
	FoundAt          time.Time `gorm:"column:found_at;comment:发现时间" json:"found_at"`
	Company          string    `gorm:"column:company;comment:关联企业" json:"company"`
	IsPublic         int       `gorm:"column:is_public;comment:是否公开: 1/公开 2/不公开" json:"is_public"`
}

func (*Other) TableName() string {
	return "intelligence_other"
}

type defaultOther struct{ *gorm.DB }

func (d *defaultOther) Create(clue *Other) (err error) {
	return d.DB.Create(clue).Error
}

func (d *defaultOther) First(opts ...mysql.HandleFunc) (Other, error) {
	query := d.DB.Model(&Other{})
	for _, opt := range opts {
		opt(query)
	}
	var clue Other
	err := query.First(&clue).Error
	return clue, err
}

func (d *defaultOther) Count(opts ...mysql.HandleFunc) (int64, error) {
	query := d.DB.Model(&Other{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	err := query.Count(&total).Error
	return total, err
}

func (d *defaultOther) List(page, size int, opts ...mysql.HandleFunc) ([]Other, int64, error) {
	query := d.DB.Model(&Other{})
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	var fakes = make([]Other, 0)
	err := query.Count(&total).Scopes(mysql.PageLimit(page, size)).Find(&fakes).Error
	return fakes, total, err
}

func (d *defaultOther) ListAll(opts ...mysql.HandleFunc) ([]*Other, error) {
	query := d.DB.Model(&Other{})
	for _, opt := range opts {
		opt(query)
	}
	var clues = make([]*Other, 0)
	err := query.Find(&clues).Error
	return clues, err
}

func (d *defaultOther) Update(clue Other) error {
	return d.DB.Updates(&clue).Error
}

func (d *defaultOther) UpdateAny(m map[string]any, opts ...mysql.HandleFunc) error {
	q := d.DB.Model(&Other{})
	for _, opt := range opts {
		opt(q)
	}
	return q.Updates(m).Error
}

func (d *defaultOther) Save(clues []*Other) error {
	if len(clues) == 0 {
		return nil
	}
	return d.DB.Save(clues).Error
}

func (d *defaultOther) DeleteById(id uint64) error {
	return d.DB.Model(&Other{}).Where("id=?", id).Delete(&Other{}).Error
}

func (d *defaultOther) DeleteByIds(id []uint64) error {
	return d.DB.Model(&Other{}).Where("id in (?)", id).Delete(&Other{}).Error
}
