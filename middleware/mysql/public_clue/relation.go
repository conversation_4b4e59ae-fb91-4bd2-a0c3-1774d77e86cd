package public_clue

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"micro-service/middleware/mysql"
	"time"
)

const PublicClueRelationTable = "public_clue_relation"

// 公共线索库关系表
type (
	PublicClueRelationModel interface {
		FindByID(uint64) (PublicClueRelation, error)
		Add(*PublicClueRelation) error
	}

	defaultPublicClueRelationModel struct {
		*gorm.DB
		table string
	}

	PublicClueRelation struct {
		Id        uint64    `gorm:"column:id;type:bigint(20) unsigned;primary_key;AUTO_INCREMENT" json:"id"`
		CreatedAt time.Time `gorm:"column:created_at;type:datetime" json:"created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at;type:datetime" json:"updated_at"`
		ClueId    int64     `gorm:"column:clue_id;type:bigint(20);comment:结果表id;NOT NULL;index:idx_clueid" json:"clue_id"`
		ParentId  string    `gorm:"column:parent_id;type:varchar(255);comment:扩展线索的父级;NOT NULL;uniqueIndex:idx_p_s" json:"parent_id"`
		SonId     int       `gorm:"column:son_id;type:tinyint(1);comment:扩展线索的子级;NOT NULL;uniqueIndex:idx_p_s" json:"son_id"`
	}
)

func (m *PublicClueRelation) TableName() string {
	return PublicClueRelationTable
}

func NewPublicClueRelationModel(conn ...*gorm.DB) PublicClueRelationModel {
	return &defaultPublicClueRelationModel{mysql.GetDbClient(conn...), PublicClueRelationTable}
}

func (d *defaultPublicClueRelationModel) FindByID(clueId uint64) (PublicClueRelation, error) {
	var pcr PublicClueRelation
	err := d.DB.Table(d.table).Where("clue_id = ?", clueId).Find(&pcr).Error
	return pcr, err
}

func (d *defaultPublicClueRelationModel) Add(r *PublicClueRelation) error {
	return d.DB.Table(d.table).Clauses(clause.Insert{Modifier: "IGNORE"}).Create(r).Error
}
