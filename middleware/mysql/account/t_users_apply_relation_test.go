package account

import (
	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
	"testing"
)

func initCfg() {
	cfg.InitLoadCfg()
	mysql.GetInstance(cfg.LoadMysql())
}
func Test_defaultTUsersApplyRelation_List(t *testing.T) {
	initCfg()

	type args struct {
		opUserId int64
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewModel().List(tt.args.opUserId)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("defaultTUsersApplyRelation.List() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}
