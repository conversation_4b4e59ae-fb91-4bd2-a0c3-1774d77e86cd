package engine_rules

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"
	"strings"
	"time"
)

const (
	RuleTableName     = "engine_rules"
	TempRuleTableName = "temp_engine_rules"
)

const (
	StatusEnable  = iota + 1 // 状态启用
	StatusDisable            // 状态禁用
)

const (
	TagScan      = iota + 1 // 扫描场景
	TagRecommend            // 推荐场景
)

const (
	TypeBuiltin = iota + 1 // 规则类型:内置(管理员管理)
	TypeCustom             // 规则类型:自定义
)

const (
	AuditStatusWait   = iota + 1 // 审核状态:待审核
	AuditStatusPass              // 审核状态:通过
	AuditStatusRefuse            // 审核状态:拒绝
)

type Ruler interface {
	Create(*Rule) error
	First(opts ...mysql.HandleFunc) (Rule, error)
	List(page, size int, opts ...mysql.HandleFunc) ([]Rule, int64, error)
	ListAll(opts ...mysql.HandleFunc) ([]*Rule, error)
	ListRuleName(opts ...mysql.HandleFunc) ([]string, error)
	ListWithRela(page, size int, userid uint64, opts ...mysql.HandleFunc) ([]FullRuleRel, int64, error)
	Update(item *Rule) error
	Updates([]Rule) error
	DeleteByIds(ids ...uint64) error
	ReplaceRulesTable([]*Rule) error
	GroupByEventType(opts ...mysql.HandleFunc) ([]string, error)
}

type Rule struct {
	dbx.ModelFull
	UserId        uint64 `gorm:"column:user_id"`
	OperateUserId uint64 `gorm:"column:operate_user_id"`
	Name          string `gorm:"column:name"`           // 规则名称
	RuleName      string `gorm:"column:rule_name"`      // 实际的规则名称
	EventDesc     string `gorm:"column:event_desc"`     // 事件描述
	EventImpact   string `gorm:"column:event_impact"`   // 事件影响
	Content       string `gorm:"column:content"`        // 用户输入的规则内容
	RuleContent   string `gorm:"column:rule_content"`   // 实际生成的内容
	EventSolution string `gorm:"column:event_solution"` // 事件解决方案
	Reason        string `gorm:"column:reason"`         // 未通过原因
	Type          int    `gorm:"column:type"`           // 类型: 内置/自定义
	Tag           int    `gorm:"column:tag"`            // 风险等级 1严重 2高 3中 4低
	Priority      int    `gorm:"column:priority"`       // 规则执行优先级
	Status        int    `gorm:"column:status"`         // 是否启用 1启用 2禁用
	AuditStatus   int    `gorm:"column:audit_status"`   // 审核状态
	EventType     string `gorm:"column:event_type"`     // 风险规则分类
}
type FullRuleRel struct {
	ID            uint64         `gorm:"primarykey;column:engine_rules_id" json:"id"`
	CreatedAt     time.Time      `gorm:"column:engine_rules_created_at;comment:创建时间" json:"created_at"`
	UpdatedAt     time.Time      `gorm:"column:engine_rules_updated_at;comment:更新时间" json:"updated_at"`
	DeletedAt     gorm.DeletedAt `gorm:"index"`
	UserId        uint64         `gorm:"column:engine_rules_user_id"`
	OperateUserId uint64         `gorm:"column:operate_user_id"`
	Name          string         `gorm:"column:name"`           // 规则名称
	RuleName      string         `gorm:"column:rule_name"`      // 实际的规则名称
	EventDesc     string         `gorm:"column:event_desc"`     // 事件描述
	EventImpact   string         `gorm:"column:event_impact"`   // 事件影响
	Content       string         `gorm:"column:content"`        // 用户输入的规则内容
	RuleContent   string         `gorm:"column:rule_content"`   // 实际生成的内容
	EventSolution string         `gorm:"column:event_solution"` // 事件解决方案
	Reason        string         `gorm:"column:reason"`         // 未通过原因
	Type          int            `gorm:"column:type"`           // 类型: 内置/自定义
	Tag           int            `gorm:"column:tag"`            // 风险等级 1严重 2高 3中 4低
	Priority      int            `gorm:"column:priority"`       // 规则执行优先级
	SysStatus     int            `gorm:"column:status"`         // 是否启用 1启用 2禁用
	AuditStatus   int            `gorm:"column:audit_status"`   // 审核状态
	EventType     string         `gorm:"column:event_type"`     // 风险规则分类
	Enable        int            `gorm:"column:enable"`         // 关联表中状态
}

func (*Rule) TableName() string {
	return RuleTableName
}

type defaultRuler struct{ *gorm.DB }

func NewRuler(db ...*gorm.DB) Ruler {
	return &defaultRuler{mysql.GetDbClient(db...)}
}

func (d *defaultRuler) Create(item *Rule) error {
	item.Priority = utils.If(item.Priority != 0, item.Priority, 1)
	return d.DB.Create(item).Error
}

func (d *defaultRuler) List(page, size int, opts ...mysql.HandleFunc) ([]Rule, int64, error) {
	query := d.DB.Model(&Rule{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var items []Rule
	if !mysql.IsPageAll(page, size) {
		query.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	err := query.Find(&items).Error
	if err != nil {
		return nil, 0, err
	}
	return items, total, nil
}

func (d *defaultRuler) ListAll(opts ...mysql.HandleFunc) ([]*Rule, error) {
	query := d.DB.Model(&Rule{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]*Rule, 0)
	err := query.Find(&list).Error
	return list, err
}

func (d *defaultRuler) ListRuleName(opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(&Rule{})
	for _, opt := range opts {
		opt(query)
	}
	var ruleName = make([]string, 0)
	err := query.Select("rule_name").Find(&ruleName).Error
	return ruleName, err
}

func (d *defaultRuler) ListWithRela(page, size int, userId uint64, opts ...mysql.HandleFunc) ([]FullRuleRel, int64, error) {
	query := d.DB.Select("*,engine_rules.user_id as engine_rules_user_id,engine_rules.id as engine_rules_id,engine_rules.created_at as engine_rules_created_at,engine_rules.updated_at as engine_rules_updated_at,COALESCE(builtin_rules_relation.enable,engine_rules.status) AS enable").Model(&Rule{}).
		Joins("left join builtin_rules_relation on engine_rules.id = builtin_rules_relation.rule_id AND builtin_rules_relation.user_id = ?", userId).Where("engine_rules.deleted_at is null")
	for _, opt := range opts {
		opt(query)
	}
	var total int64
	if !mysql.IsPageAll(page, size) {
		query.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	var rules []FullRuleRel
	err := query.Find(&rules).Error
	return rules, total, err
}

func (d *defaultRuler) First(opts ...mysql.HandleFunc) (Rule, error) {
	query := d.DB.Model(Rule{})
	for _, opt := range opts {
		opt(query)
	}
	var item Rule
	if err := query.First(&item).Error; err != nil {
		return Rule{}, err
	}
	return item, nil
}

func (d *defaultRuler) Update(item *Rule) error {
	return d.DB.Updates(item).Error
}

func (d *defaultRuler) Updates(items []Rule) error {
	if len(items) == 0 {
		return nil
	}
	return d.DB.Save(items).Error
}

func (d *defaultRuler) DeleteByIds(ids ...uint64) error {
	if len(ids) == 0 {
		return nil
	}

	query := d.DB.Where("id IN (?)", ids)

	return query.Delete(&Rule{}).Error
}

func (d *defaultRuler) ReplaceRulesTable(items []*Rule) error {
	type CreateTable struct {
		Table       string `gorm:"column:Table"`
		CreateTable string `gorm:"column:Create Table"`
	}
	var createTable CreateTable
	if err := d.DB.Raw("SHOW CREATE TABLE engine_rules").Scan(&createTable).Error; err != nil {
		return err
	}
	createTable.CreateTable = strings.Replace(createTable.CreateTable, RuleTableName, TempRuleTableName, 1)
	if err := d.DB.Exec(createTable.CreateTable).Error; err != nil {
		return err
	}

	// 创建全新记录
	ll := utils.ListSplit(items, 500)
	for x := range ll {
		if err := d.DB.Table(TempRuleTableName).Create(&ll[x]).Error; err != nil {
			return err
		}
	}
	if err := d.DB.Migrator().DropTable(RuleTableName); err != nil {
		return err
	}
	if err := d.DB.Migrator().RenameTable(TempRuleTableName, RuleTableName); err != nil {
		return err
	}
	return nil
}

func (d *defaultRuler) GroupByEventType(opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(Rule{})
	for _, opt := range opts {
		opt(query)
	}
	var categorys []string
	if err := query.Group("event_type").Find(&categorys).Error; err != nil {
		return nil, err
	}
	return categorys, nil
}
