package dataleak_doc88

import (
	"fmt"
	"time"

	"gorm.io/gorm"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
	"micro-service/pkg/utils"
)

const (
	ResultTable = "dataleak_doc88_result"
	rt          = ResultTable
	et          = RelationTable
)

type Result struct {
	dbx.Model
	// Keyword allows read, disable write permission, because of the column not in the table.
	Keyword    string `gorm:"column:keyword;<-:false"`
	Title      string `gorm:"column:title"`
	Url        string `gorm:"column:url"`
	UploadDate string `gorm:"column:upload_date"`
}

func (*Result) TableName() string {
	return ResultTable
}

func WithResultOrder(column string, asc bool) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		order := utils.If(asc, "ASC", "DESC")
		tx.Order(fmt.Sprintf("`%s`.%s %s", rt, column, order))
	}
}

func WithDateBetween(column string, start, end time.Time) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		tx.Where(fmt.Sprintf("`%s`.%s BETWEEN ? AND ?", rt, column), start, end)
	}
}

func WithKeyword(keyword string) mysql.HandleFunc {
	return func(tx *gorm.DB) {
		keyword = "%" + keyword + "%"
		tx.Where(fmt.Sprintf("%s.url LIKE ? OR %s.title LIKE ?", ResultTable, ResultTable), keyword, keyword)
	}
}

func (d *defaultDoc88) ResultSave(items []*Result) error {
	if len(items) == 0 {
		return nil
	}
	return d.Save(items).Error
}

func (d *defaultDoc88) ResultUpsert(result []*Result) ([]*Result, error) {
	if len(result) == 0 {
		return nil, nil
	}

	var urls = make([]string, 0, len(result))
	var resultMap = make(map[string]*Result, len(result))
	for _, v := range result {
		if _, ok := resultMap[v.Url]; !ok {
			resultMap[v.Url] = v
			urls = append(urls, v.Url)
		}
	}

	sp := utils.ListSplit[string](urls, 50)
	var got = make([]*Result, 0, 2*len(result))
	for i := range sp {
		l, _, err := d.ResultList(0, 0, mysql.WithValuesIn("url", sp[i]))
		if err != nil {
			return nil, err
		}
		got = append(got, l...)
	}

	var gotMap = make(map[string]*Result, len(got))
	for _, r := range got {
		gotMap[r.Url] = r
	}
	var save = make([]*Result, 0, len(result))
	for _, v := range resultMap {
		if value, ok := gotMap[v.Url]; ok {
			v.Id = value.Id
			v.CreatedAt = value.CreatedAt
		}
		save = append(save, v)
	}

	err := d.DB.Save(&save).Error
	return save, err
}

func (d *defaultDoc88) UpdateAny(id uint64, data map[string]any) error {
	if len(data) == 0 {
		return nil
	}
	return d.DB.Model(&Result{}).Where("`id`", id).Updates(data).Error
}

func (d *defaultDoc88) ResultList(page, size int, opts ...mysql.HandleFunc) ([]*Result, int64, error) {
	q := d.DB.Model(&Result{})
	for _, opt := range opts {
		opt(q)
	}

	var total int64
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}

	var items []*Result
	err := q.Find(&items).Error
	return items, total, err
}

func (d *defaultDoc88) ResultAllByTask(taskId uint64) ([]*Result, error) {
	q := d.DB.Model(&Result{})
	q.Joins(fmt.Sprintf("INNER JOIN %s ON %s.`id` = %s.`result_id`", et, rt, et))
	q.Select(fmt.Sprintf("%s.`id`,%s.`created_at`,%s.`updated_at`", et, et, et), "title", "url", "upload_date")

	var items []*Result
	err := q.Where(fmt.Sprintf("`%s`.`task_id` = ?", et), taskId).Find(&items).Error
	return items, err
}

func (d *defaultDoc88) ResultListByTaskRelation(page, size int, opts ...mysql.HandleFunc) ([]Result, int64, error) {
	q := d.DB.Model(&Relation{})
	q.Joins(fmt.Sprintf("INNER JOIN `%s` ON `%s`.`id` = `%s`.`result_id`", rt, rt, et))
	q.Joins(fmt.Sprintf("INNER JOIN `%s` ON `%s`.`id` = `%s`.`task_id`", TaskTable, TaskTable, et))
	q.Select(fmt.Sprintf("%s.*", et), "keyword", "title", "url", "upload_date")

	for _, opt := range opts {
		opt(q)
	}

	var total int64
	var l []Result
	if !mysql.IsPageAll(page, size) {
		q.Count(&total).Scopes(mysql.PageLimit(page, size))
	}
	err := q.Find(&l).Error
	return l, total, err
}
