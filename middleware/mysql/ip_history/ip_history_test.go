package ip_history

import (
	"context"
	"errors"
	"testing"
	"time"

	"micro-service/middleware/mysql"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	gmysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) (*gorm.DB, sqlmock.Sqlmock, error) {
	db, mock, err := sqlmock.New()
	if err != nil {
		return nil, nil, err
	}

	gormDB, err := gorm.Open(gmysql.New(gmysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true,
	}), &gorm.Config{})

	return gormDB, mock, err
}

func TestIpHistoryModel_GetIp(t *testing.T) {
	t.Skip()
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name       string
		page       int
		size       int
		mockSQL    func()
		wantIps    []string
		wantTotal  int64
		wantErr    bool
		setupQuery func() []mysql.HandleFunc
	}{
		{
			name: "正常分页查询",
			page: 1,
			size: 10,
			mockSQL: func() {
				countRows := sqlmock.NewRows([]string{"count"}).AddRow(2)
				mock.ExpectQuery("SELECT count").WillReturnRows(countRows)

				rows := sqlmock.NewRows([]string{"id", "ip"}).
					AddRow(1, "*******").
					AddRow(2, "*******")
				mock.ExpectQuery("SELECT").WillReturnRows(rows)
			},
			wantIps:   []string{"*******", "*******"},
			wantTotal: 2,
			wantErr:   false,
			setupQuery: func() []mysql.HandleFunc {
				return nil
			},
		},
		{
			name: "查询失败",
			page: 1,
			size: 10,
			mockSQL: func() {
				mock.ExpectQuery("SELECT count").WillReturnError(errors.New("database error"))
			},
			wantIps:   []string{},
			wantTotal: 0,
			wantErr:   true,
			setupQuery: func() []mysql.HandleFunc {
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			ips, total, err := model.GetIp(tt.page, tt.size, tt.setupQuery()...)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantTotal, total)
				assert.Equal(t, tt.wantIps, ips)
			}
		})
	}
}

func TestIpHistoryModel_FindByIp(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name      string
		userId    uint64
		ip        string
		mockSQL   func()
		wantItems []IpHistory
		wantErr   bool
	}{
		{
			name:   "查询成功",
			userId: 1,
			ip:     "*******",
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "ip", "data", "created_at", "updated_at", "threat_data", "threat_data_type"}).
					AddRow(1, 1, 1, "*******", "data1", time.Now(), time.Now(), "threat1", 1).
					AddRow(2, 1, 1, "*******", "data2", time.Now(), time.Now(), "threat2", 1)
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, "*******").
					WillReturnRows(rows)
			},
			wantItems: []IpHistory{
				{
					Id:             1,
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data1",
					ThreatData:     "threat1",
					ThreatDataType: 1,
				},
				{
					Id:             2,
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data2",
					ThreatData:     "threat2",
					ThreatDataType: 1,
				},
			},
			wantErr: false,
		},
		{
			name:   "查询失败",
			userId: 1,
			ip:     "*******",
			mockSQL: func() {
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, "*******").
					WillReturnError(errors.New("database error"))
			},
			wantItems: nil,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			items, err := model.FindByIp(tt.userId, tt.ip)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.wantItems), len(items))
				for i := range items {
					assert.Equal(t, tt.wantItems[i].Id, items[i].Id)
					assert.Equal(t, tt.wantItems[i].UserId, items[i].UserId)
					assert.Equal(t, tt.wantItems[i].CompanyId, items[i].CompanyId)
					assert.Equal(t, tt.wantItems[i].Ip, items[i].Ip)
					assert.Equal(t, tt.wantItems[i].Data, items[i].Data)
					assert.Equal(t, tt.wantItems[i].ThreatData, items[i].ThreatData)
					assert.Equal(t, tt.wantItems[i].ThreatDataType, items[i].ThreatDataType)
				}
			}
		})
	}
}

func TestIpHistoryModel_DeleteByUserAndIP(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name       string
		userAndIPs []map[uint64]string
		mockSQL    func()
		wantErr    bool
	}{
		{
			name: "成功删除",
			userAndIPs: []map[uint64]string{
				{1: "*******"},
				{2: "*******"},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("DELETE FROM `ip_history`").WillReturnResult(sqlmock.NewResult(0, 2))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name:       "空列表",
			userAndIPs: []map[uint64]string{},
			mockSQL:    func() {},
			wantErr:    false,
		},
		{
			name: "删除失败",
			userAndIPs: []map[uint64]string{
				{1: "*******"},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("DELETE FROM `ip_history`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.DeleteByUserAndIP(tt.userAndIPs)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIpHistoryModel_DeleteByUserIdAndIps(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name    string
		userId  uint64
		ips     []string
		mockSQL func()
		wantErr bool
	}{
		{
			name:   "成功删除",
			userId: 1,
			ips:    []string{"*******", "*******"},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("DELETE FROM `ip_history`").WillReturnResult(sqlmock.NewResult(0, 2))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name:    "空IP列表",
			userId:  1,
			ips:     []string{},
			mockSQL: func() {},
			wantErr: false,
		},
		{
			name:   "删除失败",
			userId: 1,
			ips:    []string{"*******"},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("DELETE FROM `ip_history`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.DeleteByUserIdAndIps(context.Background(), tt.userId, tt.ips)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIpHistoryModel_BatchCreate(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name    string
		records []*IpHistory
		mockSQL func()
		wantErr bool
	}{
		{
			name: "成功批量创建",
			records: []*IpHistory{
				{
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data1",
					ThreatData:     "threat1",
					ThreatDataType: 1,
				},
				{
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data2",
					ThreatData:     "threat2",
					ThreatDataType: 1,
				},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `ip_history`").WillReturnResult(sqlmock.NewResult(1, 2))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name:    "空记录列表",
			records: []*IpHistory{},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "创建失败",
			records: []*IpHistory{
				{
					UserId: 1,
					Ip:     "*******",
				},
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `ip_history`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.BatchCreate(tt.records)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIpHistoryModel_FindByUserIpAndType(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name           string
		userId         uint64
		companyId      int64
		ip             string
		threatDataType int32
		mockSQL        func()
		wantItems      []IpHistory
		wantErr        bool
	}{
		{
			name:           "查询成功",
			userId:         1,
			companyId:      1,
			ip:             "*******",
			threatDataType: 1,
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "ip", "data", "threat_data", "threat_data_type"}).
					AddRow(1, 1, 1, "*******", "data1", "threat1", 1)
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, 1, "*******", 1).
					WillReturnRows(rows)
			},
			wantItems: []IpHistory{
				{
					Id:             1,
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data1",
					ThreatData:     "threat1",
					ThreatDataType: 1,
				},
			},
			wantErr: false,
		},
		{
			name:           "查询失败",
			userId:         1,
			companyId:      1,
			ip:             "*******",
			threatDataType: 1,
			mockSQL: func() {
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, 1, "*******", 1).
					WillReturnError(errors.New("database error"))
			},
			wantItems: nil,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			items, err := model.FindByUserIpAndType(tt.userId, tt.companyId, tt.ip, tt.threatDataType)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.wantItems), len(items))
				for i := range items {
					assert.Equal(t, tt.wantItems[i].Id, items[i].Id)
					assert.Equal(t, tt.wantItems[i].UserId, items[i].UserId)
					assert.Equal(t, tt.wantItems[i].CompanyId, items[i].CompanyId)
					assert.Equal(t, tt.wantItems[i].Ip, items[i].Ip)
					assert.Equal(t, tt.wantItems[i].Data, items[i].Data)
					assert.Equal(t, tt.wantItems[i].ThreatData, items[i].ThreatData)
					assert.Equal(t, tt.wantItems[i].ThreatDataType, items[i].ThreatDataType)
				}
			}
		})
	}
}

func TestIpHistoryModel_CountBy(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name      string
		mockSQL   func()
		wantCount int64
		wantErr   bool
		setupOpts func() []mysql.HandleFunc
	}{
		{
			name: "成功计数",
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"count"}).AddRow(5)
				mock.ExpectQuery("SELECT count").WillReturnRows(rows)
			},
			wantCount: 5,
			wantErr:   false,
			setupOpts: func() []mysql.HandleFunc {
				return nil
			},
		},
		{
			name: "带条件计数",
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"count"}).AddRow(3)
				mock.ExpectQuery("SELECT count").WillReturnRows(rows)
			},
			wantCount: 3,
			wantErr:   false,
			setupOpts: func() []mysql.HandleFunc {
				return []mysql.HandleFunc{
					mysql.WithColumnValue("user_id", 1),
				}
			},
		},
		{
			name: "计数失败",
			mockSQL: func() {
				mock.ExpectQuery("SELECT count").WillReturnError(errors.New("database error"))
			},
			wantCount: 0,
			wantErr:   true,
			setupOpts: func() []mysql.HandleFunc {
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			count, err := model.CountBy(tt.setupOpts()...)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantCount, count)
			}
		})
	}
}

func TestIpHistoryModel_Insert(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name    string
		record  *IpHistory
		mockSQL func()
		wantErr bool
	}{
		{
			name: "成功插入",
			record: &IpHistory{
				UserId:         1,
				CompanyId:      1,
				Ip:             "*******",
				Data:           "test data",
				ThreatData:     "test threat",
				ThreatDataType: 1,
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `ip_history`").WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			wantErr: false,
		},
		{
			name: "插入失败",
			record: &IpHistory{
				UserId: 1,
				Ip:     "*******",
			},
			mockSQL: func() {
				mock.ExpectBegin()
				mock.ExpectExec("INSERT INTO `ip_history`").WillReturnError(errors.New("database error"))
				mock.ExpectRollback()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			err := model.Insert(tt.record)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestIpHistoryModel_FindByUserIpTypeAfterID(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	model := NewModel(gormDB)

	tests := []struct {
		name           string
		userId         uint64
		companyId      int64
		ip             string
		threatDataType int32
		afterId        uint64
		mockSQL        func()
		wantItems      []IpHistory
		wantErr        bool
	}{
		{
			name:           "成功查询",
			userId:         1,
			companyId:      1,
			ip:             "*******",
			threatDataType: 1,
			afterId:        5,
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "ip", "data", "threat_data", "threat_data_type"}).
					AddRow(6, 1, 1, "*******", "data1", "threat1", 1).
					AddRow(7, 1, 1, "*******", "data2", "threat2", 1)
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, 1, "*******", 1, 5).
					WillReturnRows(rows)
			},
			wantItems: []IpHistory{
				{
					Id:             6,
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data1",
					ThreatData:     "threat1",
					ThreatDataType: 1,
				},
				{
					Id:             7,
					UserId:         1,
					CompanyId:      1,
					Ip:             "*******",
					Data:           "data2",
					ThreatData:     "threat2",
					ThreatDataType: 1,
				},
			},
			wantErr: false,
		},
		{
			name:           "查询失败",
			userId:         1,
			companyId:      1,
			ip:             "*******",
			threatDataType: 1,
			afterId:        5,
			mockSQL: func() {
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, 1, "*******", 1, 5).
					WillReturnError(errors.New("database error"))
			},
			wantItems: nil,
			wantErr:   true,
		},
		{
			name:           "无匹配记录",
			userId:         1,
			companyId:      1,
			ip:             "*******",
			threatDataType: 1,
			afterId:        5,
			mockSQL: func() {
				rows := sqlmock.NewRows([]string{"id", "user_id", "company_id", "ip", "data", "threat_data", "threat_data_type"})
				mock.ExpectQuery("SELECT (.+) FROM `ip_history`").
					WithArgs(1, 1, "*******", 1, 5).
					WillReturnRows(rows)
			},
			wantItems: []IpHistory{},
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSQL()
			items, err := model.FindByUserIpTypeAfterID(tt.userId, tt.companyId, tt.ip, tt.threatDataType, tt.afterId)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.wantItems), len(items))
				for i := range items {
					assert.Equal(t, tt.wantItems[i].Id, items[i].Id)
					assert.Equal(t, tt.wantItems[i].UserId, items[i].UserId)
					assert.Equal(t, tt.wantItems[i].CompanyId, items[i].CompanyId)
					assert.Equal(t, tt.wantItems[i].Ip, items[i].Ip)
					assert.Equal(t, tt.wantItems[i].Data, items[i].Data)
					assert.Equal(t, tt.wantItems[i].ThreatData, items[i].ThreatData)
					assert.Equal(t, tt.wantItems[i].ThreatDataType, items[i].ThreatDataType)
				}
			}
		})
	}
}

func TestIpHistory_TableName(t *testing.T) {
	history := &IpHistory{}
	assert.Equal(t, "ip_history", history.TableName())
}

func TestNewModel(t *testing.T) {
	gormDB, _, err := setupTestDB(t)
	assert.NoError(t, err)

	tests := []struct {
		name string
		db   *gorm.DB
	}{
		{
			name: "使用提供的DB",
			db:   gormDB,
		},
		{
			name: "使用默认DB",
			db:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var model IpHistoryModel
			if tt.db != nil {
				model = NewModel(tt.db)
			} else {
				model = NewModel()
			}
			assert.NotNil(t, model)
			assert.IsType(t, &defaultIpHistoryModel{}, model)
		})
	}
}

func TestPaginate(t *testing.T) {
	gormDB, mock, err := setupTestDB(t)
	assert.NoError(t, err)

	tests := []struct {
		name          string
		page          int
		size          int
		expectedQuery string
		expectedArgs  []interface{}
	}{
		{
			name:          "正常分页",
			page:          2,
			size:          10,
			expectedQuery: "SELECT",
			expectedArgs:  nil,
		},
		{
			name:          "第一页",
			page:          1,
			size:          20,
			expectedQuery: "SELECT",
			expectedArgs:  nil,
		},
		{
			name:          "负页码",
			page:          -1,
			size:          10,
			expectedQuery: "SELECT",
			expectedArgs:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			paginate := Paginate(tt.page, tt.size)
			assert.NotNil(t, paginate)

			// 验证分页函数的行为
			db := paginate(gormDB)
			assert.NotNil(t, db)

			// 计算预期的偏移量
			expectedOffset := (tt.page - 1) * tt.size
			if expectedOffset < 0 {
				expectedOffset = 0
			}

			// 使用一个简单的查询来验证分页
			mock.ExpectQuery(tt.expectedQuery).
				WithArgs().
				WillReturnRows(sqlmock.NewRows([]string{"id"}).AddRow(1))

			var result struct{ ID int }
			err := db.Select("id").First(&result).Error
			assert.NoError(t, err)

			// 验证SQL语句中包含了正确的LIMIT和OFFSET
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}
