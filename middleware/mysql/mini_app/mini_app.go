package mini_app

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type MiniApper interface {
	ListAll(opts ...mysql.HandleFunc) ([]*App, error)
}

const MiniAppTableName = "mini_app"

const (
	Online  = iota + 1 // 在线
	Offline            // 离线
)

type App struct {
	dbx.Model
	Name        string `gorm:"column:name"`
	CompanyName string `gorm:"column:company_name"`
	Account     string `gorm:"column:account"`
	OriginId    string `gorm:"column:origin_id"`
	Description string `gorm:"column:description"`
	Erweima     string `gorm:"column:erweima"`   // 地址
	Logo        string `gorm:"column:logo"`      // logo 地址
	Platform    int    `gorm:"column:platform"`  // 平台 1:微信
	IsOnline    int    `gorm:"column:is_online"` // 是否上线 1:在线 2:离线
}

func (*App) TableName() string {
	return MiniAppTableName
}

type defaultMiniApp struct{ *gorm.DB }

func NewMiniApp(db ...*gorm.DB) MiniApper {
	return &defaultMiniApp{mysql.GetDbClient(db...)}
}

func WithKeyword(name string) mysql.HandleFunc {
	return func(db *gorm.DB) {
		name = "%" + name + "%"
		db.Where("`name` LIKE ? OR `company_name` LIKE ?", name, name)
	}
}

func (d *defaultMiniApp) ListAll(opts ...mysql.HandleFunc) ([]*App, error) {
	query := d.DB.Model(&App{})
	for _, opt := range opts {
		opt(query)
	}

	var apps []*App
	err := query.Find(&apps).Error
	return apps, err
}
