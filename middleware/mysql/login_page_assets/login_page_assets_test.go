package login_page_assets

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())
}

func TestDefaultLoginPageAssetsModel_CountByUserID(t *testing.T) {
	initCfg()

	to, err := NewModel(mysql.GetInstance()).CountByUserID(482, "", "", 1)
	if err != nil {
		t.Error(err)
		return
	}
	fmt.Println(to)
}

func TestDefaultLoginPageAssetsModel_UpsertByKey(t *testing.T) {
	initCfg()

	var save []*LoginPageAssets
	save = append(save, &LoginPageAssets{
		Id:        33,
		UserId:    1,
		CompanyId: 0,
		Title:     "WSO2 Management Console",
		Ip:        "************",
		Port:      "40072",
		Url:       "https://************:40072",
		ImgUrl:    "",
		Status:    1,
		Node:      "",
		UniqueKey: "1-************-40072-https://************:40072",
	})
	save = append(save, &LoginPageAssets{
		Id:        0,
		UserId:    1,
		CompanyId: 0,
		Title:     "WSO2 Management Console",
		Ip:        "************",
		Port:      "443",
		Url:       "x",
		ImgUrl:    "",
		Status:    1,
		Node:      "",
		UniqueKey: "1-************-443-x",
	})

	err := NewModel().UpsertByKey(1, save)
	assert.Nil(t, err)
}

func TestDefaultLoginPageAssetsModel_CombineDetectTaskId(t *testing.T) {
	initCfg()

	testCases := []struct {
		originDetectTaskId string
		detectTaskId       string
		expected           string
	}{
		{originDetectTaskId: "", detectTaskId: "1", expected: "[\"1\"]"},
		{originDetectTaskId: "[\"1\"]", detectTaskId: "", expected: "[\"1\"]"},
		{originDetectTaskId: "[\"1\"]", detectTaskId: "1", expected: "[\"1\"]"},
		{originDetectTaskId: "[\"1\"]", detectTaskId: "2", expected: "[\"1\",\"2\"]"},
		{originDetectTaskId: "[\"1\",\"2\"]", detectTaskId: "3", expected: "[\"1\",\"2\",\"3\"]"},
	}
	for _, testCase := range testCases {
		detectTaskId := combineDetectTaskId(testCase.originDetectTaskId, testCase.detectTaskId)
		assert.Equal(t, testCase.expected, detectTaskId)
	}
}
