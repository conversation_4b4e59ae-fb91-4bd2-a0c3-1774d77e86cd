package login_page_assets

import (
	"encoding/json"
	"micro-service/middleware/mysql"
	"micro-service/pkg/utils"
	"time"

	"gorm.io/gorm"
)

// 登录页面数据统计
type (
	LoginPageAssetsModel interface {
		List(page, size int64, opts ...mysql.HandleFunc) ([]LoginPageAssets, int64, error)
		ListAll(opts ...mysql.HandleFunc) ([]LoginPageAssets, error)
		ListTitle(userId uint64, opts ...mysql.HandleFunc) ([]string, error)
		CountByUserID(userID uint64, start, end string, status ...int) (int64, error)
		UpsertByKey(userId uint64, list []*LoginPageAssets) error
		DeleteByID(userId uint64, idList ...uint64) error
		DeleteAny(opts ...mysql.HandleFunc) error
		UpdateAny(updates map[string]any, opts ...mysql.HandleFunc) error
		GetStatusCount(userId uint64) (pending, confirmed, rejected int64, err error)
		CreateDistinctByUrl(userId uint64, list []*LoginPageAssets) error
	}

	defaultLoginPageAssetsModel struct {
		*gorm.DB
		table string
	}

	LoginPageAssets struct {
		Id               uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		UserId           uint64    `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
		CompanyId        int64     `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
		Title            string    `gorm:"column:title;type:varchar(255);comment:网站标题;NOT NULL" json:"title"`
		Ip               string    `gorm:"column:ip;type:varchar(255);comment:资产的ip;NOT NULL" json:"ip"`
		Port             string    `gorm:"column:port;type:varchar(255);comment:资产的port;NOT NULL" json:"port"`
		Url              string    `gorm:"column:url;type:varchar(255);comment:登录页面url;NOT NULL" json:"url"`
		OriginUrl        string    `gorm:"-" json:"origin_url"` // ignore the column in R/W table
		ImgUrl           string    `gorm:"column:img_url;type:varchar(255);comment:登录页面截图" json:"img_url"`
		Status           int       `gorm:"column:status;type:tinyint(4);default:0;comment:状态 0/待审核 1/审核通过 2/审核驳回;NOT NULL" json:"status"`
		Node             string    `gorm:"column:node;type:varchar(128);comment:执行节点" json:"node"`
		UniqueKey        string    `gorm:"column:unique_key;type:varchar(128);comment:唯一值，user_id-ip-端口-url组成" json:"unique_key"`
		CreatedAt        time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt        time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
		BatchId          string    `gorm:"column:batch_id;type:varchar(64);comment:导入批次ID" json:"batch_id"`
		DetectTaskId     string    `gorm:"column:detect_task_id;type:bigint(20) unsigned;comment:检测任务ID" json:"detect_task_id"`
		WebsiteMessageId uint64    `gorm:"column:website_message_id;type:bigint(20) unsigned;comment:网站消息ID" json:"website_message_id"`
	}
)

const TableName = "login_page_assets"

func (m *LoginPageAssets) TableName() string {
	return TableName
}

func NewModel(conn ...*gorm.DB) LoginPageAssetsModel {
	return &defaultLoginPageAssetsModel{mysql.GetDbClient(conn...), TableName}
}

func (d *defaultLoginPageAssetsModel) CountByUserID(userID uint64, start, end string, status ...int) (int64, error) {
	var total int64
	q := d.DB.Model(&LoginPageAssets{})
	q.Where("user_id = ?  ", userID)
	if len(status) > 0 {
		q.Where("status = ?  ", status[0])
	}

	if start != "" && end != "" {
		q.Where("created_at between ? and ?", start, end)

	}
	e := q.Count(&total).Error
	return total, e
}

func (d *defaultLoginPageAssetsModel) Save(list []*LoginPageAssets) error {
	if len(list) == 0 {
		return nil
	}
	return d.DB.Save(list).Error
}

func (d *defaultLoginPageAssetsModel) UpsertByKey(userId uint64, list []*LoginPageAssets) error {
	if len(list) == 0 {
		return nil
	}

	q := d.DB.Model(&LoginPageAssets{})
	keys := make([]string, 0, len(list))
	for i := range list {
		keys = append(keys, list[i].UniqueKey)
	}
	split := utils.ListSplit[string](keys, 60)
	existKeys := make(map[string]struct{}, len(list))
	for i := range split {
		result := make([]*LoginPageAssets, 0, len(split[i]))
		err := q.Where("`user_id` = ? AND `unique_key` IN (?)", userId, split[i]).Find(&result).Error
		if err != nil {
			return err
		}
		var save = make([]*LoginPageAssets, 0, len(split[i]))
		for j := range list {
			for _, v := range result {
				if list[i].UniqueKey == v.UniqueKey {
					list[j].Id = v.Id
					list[j].CreatedAt = v.CreatedAt
					list[j].DetectTaskId = combineDetectTaskId(v.DetectTaskId, list[j].DetectTaskId)
					save = append(save, list[j])
					existKeys[v.UniqueKey] = struct{}{}
					break
				}
			}
		}
		if errSave := d.Save(save); errSave != nil {
			return errSave
		}
	}

	notExistList := make([]*LoginPageAssets, 0, len(list))
	for i := range list {
		if _, ok := existKeys[list[i].UniqueKey]; !ok {
			notExistList = append(notExistList, list[i])
		}
	}
	if errSave := d.Save(notExistList); errSave != nil {
		return errSave
	}
	return nil
}

// combineDetectTaskId 合并检测任务ID
func combineDetectTaskId(originDetectTaskId string, detectTaskId string) string {
	result := make([]string, 0)
	// 如果两个都为空，返回空
	if originDetectTaskId == "" && detectTaskId == "" {
		return ""
	}
	// 如果originDetectTaskId为空，则直接将detectTaskId添加到result中
	if originDetectTaskId == "" {
		result = append(result, detectTaskId)
	} else if detectTaskId == "" {
		// 如果detectTaskId为空，则直接返回originDetectTaskId
		return originDetectTaskId
	} else {
		// 如果两个都不为空，则将originDetectTaskId转换为数组，并添加detectTaskId
		err := json.Unmarshal([]byte(originDetectTaskId), &result)
		if err != nil {
			return ""
		}
		result = append(result, detectTaskId)
		// 去重
		result = utils.ListDistinctNonZero(result)
	}
	if len(result) == 0 {
		return ""
	}
	jsonData, err := json.Marshal(result)
	if err != nil {
		return ""
	}
	return string(jsonData)
}

func (d *defaultLoginPageAssetsModel) List(page, size int64, opts ...mysql.HandleFunc) ([]LoginPageAssets, int64, error) {
	query := d.DB.Model(&LoginPageAssets{})
	for _, opt := range opts {
		opt(query)
	}

	var total int64
	var list = make([]LoginPageAssets, 0, size)
	if !mysql.IsPageAll(int(page), int(size)) {
		query.Count(&total).Scopes(mysql.PageLimit(int(page), int(size)))
	}
	err := query.Find(&list).Error
	if err != nil {
		return nil, 0, err
	}
	return list, total, nil
}

func (d *defaultLoginPageAssetsModel) ListTitle(userId uint64, opts ...mysql.HandleFunc) ([]string, error) {
	query := d.DB.Model(&LoginPageAssets{}).Distinct("title").Where("user_id = ?", userId)
	for _, opt := range opts {
		opt(query)
	}
	var list []string
	err := query.Find(&list).Error
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (d *defaultLoginPageAssetsModel) DeleteByID(userId uint64, idList ...uint64) error {
	if len(idList) == 0 {
		return nil
	}
	query := d.DB.Model(&LoginPageAssets{}).Where("user_id = ?", userId).Where("id IN (?)", idList)
	if err := query.Delete(&LoginPageAssets{}).Error; err != nil {
		return err
	}
	return nil
}

func (d *defaultLoginPageAssetsModel) DeleteAny(opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&LoginPageAssets{})
	for _, opt := range opts {
		opt(query)
	}
	if err := query.Delete(&LoginPageAssets{}).Error; err != nil {
		return err
	}
	return nil
}

func (d *defaultLoginPageAssetsModel) UpdateAny(updates map[string]any, opts ...mysql.HandleFunc) error {
	query := d.DB.Model(&LoginPageAssets{})
	for _, opt := range opts {
		opt(query)
	}
	if len(updates) == 0 {
		return nil
	}
	return query.Updates(updates).Error
}

func (d *defaultLoginPageAssetsModel) GetStatusCount(userId uint64) (pending, confirmed, rejected int64, err error) {
	query := d.DB.Model(&LoginPageAssets{}).Select("status").Where("user_id = ?", userId)
	var count []int64
	if err = query.Find(&count).Error; err != nil {
		return
	}
	for _, c := range count {
		switch c {
		case 0:
			pending++
		case 1:
			confirmed++
		case 2:
			rejected++
		}
	}
	return
}

func (d *defaultLoginPageAssetsModel) ListAll(opts ...mysql.HandleFunc) ([]LoginPageAssets, error) {
	query := d.DB.Model(&LoginPageAssets{})
	for _, opt := range opts {
		opt(query)
	}
	var list []LoginPageAssets
	if err := query.Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (d *defaultLoginPageAssetsModel) CreateDistinctByUrl(userId uint64, list []*LoginPageAssets) error {
	for _, v := range list {
		err := d.DB.Model(&LoginPageAssets{}).Where("user_id = ?", userId).Where("url = ?", v.Url).FirstOrCreate(v).Error
		if err != nil {
			return err
		}
	}
	return nil
}
