package auth_access_client

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-oauth2/oauth2/v4"
	"github.com/go-oauth2/oauth2/v4/models"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"

	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

const (
	AuthAccessClientStatusEnable = 1    // 启用状态
	AccountTypeDefault           = iota // 默认用户
	AccountTypeZh                       // 中航金网账户
	AccountTypeFORadarLocal             // foradar本地化
	AccountTypeFD01                     // fd01本地化账号
	AccountTypeGoBy                     // Goby用户
)

const (
	ClientTypeDefault = iota
	ClientTypeForadar
	ClientTypeFd01
	ClientTypeGoby
)

type (
	AuthAccessClientModel interface {
		FindById(id interface{}) (*AuthAccessClient, error)
		First(opts ...mysql.HandleFunc) (AuthAccessClient, error)
		Create(info oauth2.ClientInfo, scope []string) error
		Add(*AuthAccessClient) error
		GetScope(clientId, secretKey string) (string, string)
		GetClientInfoByID(clientId string) (*AuthAccessClient, error)
		GetByID(ctx context.Context, clientId string) (oauth2.ClientInfo, error)
		UpdateLimitByID(clientId string, assetsCount int32, usedCount int32, fieldType int8) error
		UpdateApiQuota(clientId string, quotaChange int32) error
		DisableClient(clientId string) error
		ListAll(opts ...mysql.HandleFunc) ([]*AuthAccessClient, error)
	}

	defaultAuthAccessClientModel struct {
		*gorm.DB
		table string
	}

	AuthAccessClient struct {
		models.Client
		dbx.Model
		CompanyName        string    `gorm:"column:company_name;type:varchar(255);not null;comment:企业名称"`
		Domain             string    `gorm:"type:varchar(512);comment:'授权域名'"`
		UserId             string    `gorm:"type:varchar(512);comment:'UserId'"`
		Status             string    `gorm:"type:tinyint(4);comment:'客户端状态:0/1 启用/禁用'"`
		ClientId           string    `gorm:"type:varchar(512);comment:'ClientId'"`
		Secret             string    `gorm:"type:varchar(512);comment:'SecretKey'"`
		Data               string    `gorm:"type:text;comment:'ClientInfo'"`
		Type               int       `gorm:"type:tinyint(4);comment:'客户端类型'"`
		Scope              string    `gorm:"type:longtext;comment:'scope'"`
		Desc               string    `gorm:"type:varchar(512);comment:'客户端说明'"`
		ExpiredAt          time.Time `gorm:"column:expired_at;type:datetime;not null;comment:过期时间"`
		IpWhitelist        string    `gorm:"column:ip_whitelist;type:varchar(1000);comment:IP白名单"`
		AccountType        int8      `gorm:"column:account_type;type:int(8);default:0;comment:账户类型 0/1 默认账户/金航中网账户"`
		DateKey            int32     `gorm:"column:date_key;type:int(11) unsigned;default:0;comment:日期key"`
		PerCnameLimit      int32     `gorm:"column:per_cname_limit;type:int(11) unsigned;default:0;comment:单次企业查询次数限制"`
		DayAssetsLimit     int32     `gorm:"column:day_assets_limit;type:int(11) unsigned;default:0;comment:单日企业资产数限制"`
		MonDetectLimit     int32     `gorm:"column:mon_detect_limit;type:int(11) unsigned;default:0;comment:月企业查询次数限制"`
		DayAssetsCount     int32     `gorm:"column:day_assets_count;type:int(11) unsigned;default:0;comment:单日资产数量"`
		UsedDetectCount    int32     `gorm:"column:used_detect_count;type:int(11) unsigned;default:0;comment:单月查询次数统计"`
		DayClientTaskLimit int       `gorm:"column:day_client_task_limit"`
		ClientTaskTotal    int       `gorm:"column:client_task_total"`
		LocalArch          int       `gorm:"column:local_arch"` // 本地化部署架构 1-X86 2-arm
		ApiQuotaRemaining  int32     `gorm:"column:api_quota_remaining;type:int(11) unsigned;default:5000000;comment:API剩余次数"`
	}
)

// AuthAccessClientTable 表名
const AuthAccessClientTable = "auth_access_clients"

func (c *AuthAccessClient) TableName() string {
	return AuthAccessClientTable
}

func NewAuthAccessClientModel(conn ...*gorm.DB) AuthAccessClientModel {
	return &defaultAuthAccessClientModel{
		DB:    mysql.GetDbClient(conn...),
		table: AuthAccessClientTable,
	}
}

func (d *defaultAuthAccessClientModel) First(opts ...mysql.HandleFunc) (AuthAccessClient, error) {
	query := d.DB.Table(d.table)
	for _, opt := range opts {
		opt(query)
	}
	var client AuthAccessClient
	err := query.First(&client).Error
	if err != nil {
		return AuthAccessClient{}, err
	}
	return client, nil
}

func (d *defaultAuthAccessClientModel) ListAll(opts ...mysql.HandleFunc) ([]*AuthAccessClient, error) {
	query := d.DB.Model(&AuthAccessClient{})
	for _, opt := range opts {
		opt(query)
	}
	var list = make([]*AuthAccessClient, 0)
	err := query.Find(&list).Error
	return list, err
}

func (d *defaultAuthAccessClientModel) Add(item *AuthAccessClient) error {
	return d.DB.Table(d.table).Create(item).Error
}

func (d *defaultAuthAccessClientModel) FindById(id interface{}) (*AuthAccessClient, error) {
	var client AuthAccessClient
	err := d.DB.Table(d.table).Where("id = ?", id).Find(&client).Error
	if err != nil {
		return nil, err
	}
	return &client, nil
}

func (d *defaultAuthAccessClientModel) Create(info oauth2.ClientInfo, scope []string) error {
	data, err := json.Marshal(info)
	if err != nil {
		return err
	}
	client := &AuthAccessClient{
		Secret:   info.GetSecret(),
		ClientId: info.GetID(),
		Data:     string(data),
		Scope:    strings.Join(scope, ","),
	}
	return d.DB.Table(d.table).Create(client).Error
}

func (d *defaultAuthAccessClientModel) GetScope(clientId, secretKey string) (string, string) {
	accessClient := &AuthAccessClient{}
	// 查找客户端授权
	d.DB.Table(d.table).Limit(1).Where("status", AuthAccessClientStatusEnable).Find(
		accessClient,
		"(client_id=? and secret=?)",
		clientId, secretKey,
	)
	return accessClient.UserId, accessClient.Scope
}

func (d *defaultAuthAccessClientModel) GetClientInfoByID(clientId string) (*AuthAccessClient, error) {
	if clientId == "" {
		return nil, fmt.Errorf("clientId cannt empty")
	}

	// 查找客户端授权
	accessClient := &AuthAccessClient{}
	query := d.DB.Table(d.table).Where("status", AuthAccessClientStatusEnable).Where("client_id = ?", clientId)

	err := query.First(accessClient).Error

	return accessClient, err
}

func (d *defaultAuthAccessClientModel) GetByID(_ context.Context, clientId string) (oauth2.ClientInfo, error) {
	if clientId == "" {
		return nil, errors.New("not Found")
	}
	accessClient := &AuthAccessClient{}
	// 查找客户端授权
	d.DB.Table(d.table).Limit(1).Where("status", AuthAccessClientStatusEnable).Find(
		accessClient,
		"client_id=?", clientId,
	)

	return &models.Client{
		ID:     accessClient.ClientId,
		Secret: accessClient.Secret,
		UserID: accessClient.UserId,
	}, nil
}

// GetID client id
func (c *AuthAccessClient) GetID() string {
	return c.ClientId
}

// GetSecret client domain
func (c *AuthAccessClient) GetSecret() string {
	return c.Secret
}

// GetDomain client domain
func (c *AuthAccessClient) GetDomain() string {
	return c.Domain
}

// GetUserID user id
func (c *AuthAccessClient) GetUserID() string {
	return c.UserId
}

// IsPublic is public
func (c *AuthAccessClient) IsPublic() bool {
	return c.Public
}

// UpdateLimitByID fieldType 1/2/3 更新统计数和月使用次数/只更新日统计数/只更新月使用次数
func (d *defaultAuthAccessClientModel) UpdateLimitByID(clientId string, assetsCount int32, usedCount int32, fieldType int8) error {
	if clientId == "" {
		return errors.New("not Found")
	}

	if fieldType == 2 {
		usedCount = 1
	}

	if fieldType == 3 {
		assetsCount = 1
	}

	// 查询原有的数据
	var authAccessClientInfo = &AuthAccessClient{}
	if err := d.DB.Table(d.table).Where("client_id = ?", clientId).First(authAccessClientInfo).Error; err != nil {
		return err
	}

	nowTimeStr := time.Now().Format("20060102")
	oldDateKey := authAccessClientInfo.DateKey
	oldDateKeyStr := strconv.Itoa(int(oldDateKey))
	nowDateKey, _ := strconv.ParseInt(nowTimeStr, 10, 32)
	if authAccessClientInfo.DateKey != int32(nowDateKey) {
		authAccessClientInfo.DayAssetsCount = 0
		if fieldType == 3 {
			assetsCount = 0
		}
	}
	if oldDateKey != 0 {
		if nowTimeStr[:len(nowTimeStr)-2] != oldDateKeyStr[:len(oldDateKeyStr)-2] {
			if fieldType == 2 {
				usedCount = 0
			}
			authAccessClientInfo.DayAssetsCount = 0
			authAccessClientInfo.UsedDetectCount = 0
		}
	} else {
		if fieldType == 2 {
			usedCount = 0
		}
		authAccessClientInfo.DayAssetsCount = 0
		authAccessClientInfo.UsedDetectCount = 0
	}

	// 根据不同类型修改需要更新的字段
	updateFields := make(map[string]interface{})
	updateFields["date_key"] = nowDateKey
	if fieldType == 1 {
		updateFields["day_assets_count"] = authAccessClientInfo.DayAssetsCount + assetsCount
		updateFields["used_detect_count"] = authAccessClientInfo.UsedDetectCount + usedCount
	} else if fieldType == 2 {
		updateFields["day_assets_count"] = authAccessClientInfo.DayAssetsCount + assetsCount
	} else if fieldType == 3 {
		updateFields["used_detect_count"] = authAccessClientInfo.UsedDetectCount + usedCount
	}

	// 如果某个字段要更新为0，则需要特殊处理
	if assetsCount == 0 {
		updateFields["day_assets_count"] = gorm.Expr("0")
	}
	if usedCount == 0 {
		updateFields["used_detect_count"] = gorm.Expr("0")
	}
	// 执行更新操作
	err := d.DB.Table(d.table).Where("client_id = ?", clientId).Updates(updateFields).Error
	if err != nil {
		return err
	}

	return nil
}

// UpdateApiQuota 更新API配额
func (d *defaultAuthAccessClientModel) UpdateApiQuota(clientId string, quotaChange int32) error {
	if clientId == "" {
		return errors.New("clientId cannot be empty")
	}

	// 使用原子操作更新配额
	result := d.DB.Table(d.table).
		Where("client_id = ?", clientId).
		Update("api_quota_remaining", gorm.Expr("api_quota_remaining + ?", quotaChange))

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("client not found")
	}

	return nil
}

// DisableClient 禁用客户端
func (d *defaultAuthAccessClientModel) DisableClient(clientId string) error {
	if clientId == "" {
		return errors.New("clientId cannot be empty")
	}

	result := d.DB.Table(d.table).
		Where("client_id = ?", clientId).
		Update("status", "0")

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("client not found")
	}

	return nil
}
