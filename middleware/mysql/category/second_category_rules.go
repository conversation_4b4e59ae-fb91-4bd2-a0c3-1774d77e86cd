package category

import (
	"gorm.io/gorm"
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"
)

type (
	SecondCategoryRuleModel interface {
	}

	defaultSecondCategoryRuleModel struct {
		*gorm.DB
		table string
	}

	SecondCategoryRule struct {
		dbx.Model
		SecondCategoryId uint64 `gorm:"column:second_category_id" json:"second_category_id"`
		RuleId           uint64 `gorm:"column:rule_id" json:"rule_id"`
	}
)

var _ SecondCategoryRuleModel = (*defaultSecondCategoryRuleModel)(nil)

const secondCategoryRuleTable = "second_category_rules"

// TableName 表名
func (r *SecondCategoryRule) TableName() string {
	return secondCategoryRuleTable
}

func NewSecondCategoryRuleModel(clients ...*gorm.DB) SecondCategoryRuleModel {
	return &defaultSecondCategoryRuleModel{
		DB:    mysql.GetDbClient(clients...),
		table: secondCategoryRuleTable,
	}
}
