package apps

import (
	"fmt"
	"testing"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/assert"

	"micro-service/initialize/mysql"
	"micro-service/pkg/cfg"
)

func initCfg() {
	err := godotenv.Load("../../../.env")
	if err != nil {
		panic(err)
	}
	cfg.InitLoadCfg()
	_ = mysql.GetInstance(cfg.LoadMysql())
}

func Test_AppsUpsert(t *testing.T) {
	initCfg()

	var appList []Apps
	appList = append(appList, Apps{
		Name:        "Android-app",
		Logo:        "NULL",
		Url:         "https://www.baidu.com",
		Category:    "Life",
		CompanyName: "",
		Platform:    "Android",
		Developer:   "Android-Dev",
		Product:     "",
		Area:        "",
		IsOnline:    1,
	})
	err := NewAppsModel().AppsUpsert(appList)
	assert.Nil(t, err)
	assert.Greater(t, appList[0].Id, uint64(0))
}

func Test_ListAppByHistory(t *testing.T) {
	initCfg()

	_, _, err := NewAppsModel().
		ListAppByHistory(1, 1, WithKeyword("北京", "name", "keyword"))
	assert.Nil(t, err)
}

func Test_AppsGroup(t *testing.T) {
	initCfg()

	db := NewAppsModel()
	companies, err := db.AppsGroup(34, "company_name")
	assert.Nil(t, err)
	fmt.Println(companies)
}
