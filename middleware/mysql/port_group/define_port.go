package port_group

import (
	"micro-service/middleware/mysql"
	"micro-service/pkg/dbx"

	"gorm.io/gorm"
)

type (
	DefinePort struct {
		dbx.Model
		TaskId         uint64 `json:"task_id"`
		CronTaskId     uint64 `json:"cron_task_id"`
		UserId         uint64 `json:"user_id"`
		PortProtocolId uint64 `json:"port_protocol_id"`
		Port           int    `json:"port"`

		// 移除错误的many2many关联，DefinePort通过PortProtocolId字段直接关联协议
		// Protocols []*PortProtocol `gorm:"many2many:port_port_protocol;"`
	}

	defaultDefinePortModel struct {
		*gorm.DB
		table string
	}
	DefinePortModel interface {
		CountByTaskId(taskID uint64) int
		FindWithProtocolsByTaskId(taskID uint64) ([]*DefinePort, error)
		FindWithProtocolsByCronTaskId(cronTaskID uint64) ([]*DefinePort, error)
		CreateBatch(definePorts []*DefinePort) error
	}
)

const definePortTable = "define_ports"

// TableName 为 DefinePort 结构体指定表名
func (dp *DefinePort) TableName() string {
	return definePortTable
}

func (d *defaultDefinePortModel) TableName() string {
	return definePortTable
}

func NewDefinePortModel(db ...*gorm.DB) *defaultDefinePortModel {
	return &defaultDefinePortModel{mysql.GetDbClient(db...), definePortTable}
}

func (d *defaultDefinePortModel) CreateBatch(definePorts []*DefinePort) error {
	return d.DB.CreateInBatches(definePorts, 100).Error
}

func (d *defaultDefinePortModel) CountByTaskId(taskID uint64) int {
	var count int64
	err := d.DB.Table(d.table).Where("task_id = ?", taskID).Count(&count).Error
	if err != nil {
		return 0
	}
	return int(count)
}

func (d *defaultDefinePortModel) FindWithProtocolsByTaskId(taskID uint64) ([]*DefinePort, error) {
	var ports []*DefinePort
	err := d.DB.Table(d.table).Where("task_id = ?", taskID).Find(&ports).Error
	if err != nil {
		return nil, err
	}
	return ports, nil
}

func (d *defaultDefinePortModel) FindWithProtocolsByCronTaskId(cronTaskID uint64) ([]*DefinePort, error) {
	var ports []*DefinePort
	err := d.DB.Table(d.table).Where("cron_task_id = ?", cronTaskID).Find(&ports).Error
	if err != nil {
		return nil, err
	}
	return ports, nil
}
