package port_group

import (
	"fmt"
	"micro-service/middleware/mysql"
	pb "micro-service/webService/proto"
	"time"

	"gorm.io/gorm"
)

const portTable = "ports"

var PortStatusMap = map[int32]string{
	0: "正常",
	1: "禁用",
}

const (
	SourceDefault = 0
	SourceAdd     = 1
	StatusDefault = 0
	StatusDisable = 1
)

const (
	IpTypev4 = iota + 1 // ip类型: ipv4
	IpTypev6            // ip类型: ipv6
)

// 端口协议类型常量
const (
	PortProtocolTypeTCP = 0 // TCP协议
	PortProtocolTypeUDP = 1 // UDP协议
)

// 端口来源常量
const (
	PortSourceDefault = 0 // 系统预置
	PortSourceAdd     = 1 // 用户添加
)

const (
	PortTotal    = "0-65535"
	ScanAllPorts = "0-65535,U:53,U:67,U:69,U:80,U:88,U:113,U:123,U:137,U:161,U:162,U:391,U:443,U:500,U:520,U:623,U:626,U:705,U:853,U:1027,U:1194,U:1434,U:1604,U:1645,U:1701,U:1812,U:1900,U:1967,U:1993,U:2083,U:2094,U:2123,U:2152,U:2424,U:2427,U:3283,U:3333,U:3391,U:3478,U:3671,U:3702,U:3784,U:4050,U:4070,U:4500,U:4800,U:5000,U:5001,U:5002,U:5004,U:5005,U:5006,U:5007,U:5008,U:5050,U:5060,U:5061,U:5093,U:5094,U:5095,U:5351,U:5353,U:5357,U:5554,U:5632,U:5673,U:5683,U:6002,U:6003,U:6006,U:6060,U:6881,U:6969,U:7000,U:7001,U:7003,U:7005,U:8002,U:8080,U:8888,U:9000,U:9100,U:9600,U:10001,U:17185,U:20000,U:28784,U:30310,U:30311,U:30312,U:30313,U:30718,U:32768,U:34962,U:34963,U:34964,U:44818,U:47808,U:48899,U:59110"
)

type (
	PortModel interface {
		FindById(Id uint64, UserId uint64, WithDetail bool) (*Port, error)
		GetIndexData(userId uint64, req *pb.PortIndexRequest, Page, Size int) ([]Port, int64, error)
		GetIndexList(userId uint64) ([]Port, error)
		PortStore(req *PortChangeParam) error
		PortDel(req *pb.PortDelRequest) error
		GetPortProtocolList(req *pb.PortProtocolIndexRequest) ([]PortProtocol, error)
		PortEdit(req *PortChangeParam) error
		PortUpdateStatus(req *pb.PortUpdateStatusRequest) error
		CountByIds(ids []uint64, status int32) int
		GetByIds(ids []uint64, status int32) ([]*Port, error)
		CountByUserIds(userIds uint64, status int32) int
		FindWithProtocolsByIDs(ids []uint64) ([]*Port, error)
		FindProtocolById(id uint64) (*PortProtocol, error)
	}

	defaultPortModel struct {
		*gorm.DB
		table string
	}

	Port struct {
		Id        uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
		UserId    uint64    `gorm:"column:user_id;type:bigint(20) unsigned;comment:用户id;NOT NULL" json:"user_id"`
		CompanyId int64     `gorm:"column:company_id;type:bigint(20) unsigned;comment:企业ID" json:"company_id"`
		Port      uint32    `gorm:"column:port;type:int(10) unsigned;comment:端口号" json:"port"`
		Status    int32     `gorm:"column:status;type:tinyint(4);comment:0/启用 1/禁用" json:"status"`
		Source    int32     `gorm:"column:source;type:tinyint(4);comment:0/启用 1/禁用" json:"source"`
		CreatedAt time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
		UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`

		Protocols []*PortProtocol `gorm:"many2many:port_port_protocol;"`
		Groups    []*PortGroup    `gorm:"many2many:port_port_group;"`
	}
)

func (p *Port) TableName() string {
	return portTable
}

func NewPortModel(conn ...*gorm.DB) PortModel {
	return &defaultPortModel{mysql.GetDbClient(conn...), portTable}
}

type PortProtocol struct {
	Id        uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
	Protocol  string    `gorm:"type:varchar(255);default:'';comment:协议" json:"protocol"`
	Type      int32     `gorm:"column:type;type:tinyint(4);comment:类型 0:TCP; 1:UDP" json:"type"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`

	Ports []*Port `gorm:"many2many:port_port_protocol;"`
}

func (pp *PortProtocol) TableName() string {
	return "port_protocols"
}

type PortPortProtocol struct {
	Id             uint64    `gorm:"column:id;type:bigint(20) unsigned;AUTO_INCREMENT;primary_key" json:"id"`
	PortId         uint64    `gorm:"column:port_id;type:bigint(20) unsigned;comment:端口id;NOT NULL" json:"port_id"`
	PortProtocolId uint64    `gorm:"column:port_protocol_id;type:bigint(20) unsigned;comment:协议id;NOT NULL" json:"port_protocol_id"`
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;type:timestamp" json:"updated_at"`
}

func (pp *PortPortProtocol) TableName() string {
	return "port_port_protocol"
}

func (d *defaultPortModel) GetIndexData(userId uint64, req *pb.PortIndexRequest, Page, Size int) ([]Port, int64, error) {
	var total int64
	//查询语法
	query := d.DB.Table(d.table)

	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}
	if req.Keyword != "" {
		query = query.Where("port LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))
	}

	if len(req.ProtocolId) > 0 && req.ProtocolId[0] != 0 {
		query.Where("EXISTS (?)", d.DB.Table("port_protocols").Joins("inner join `port_port_protocol` on `port_protocols`.`id` = `port_port_protocol`.`port_protocol_id` ").
			Where("`ports`.`id` = `port_port_protocol`.`port_id`").
			Where("port_protocols.id IN (?)", req.ProtocolId))
	}

	if len(req.PortGroupId) > 0 && req.PortGroupId[0] != 0 {
		query = query.Where("EXISTS (?)", d.DB.Table("port_groups").Joins("inner join `port_port_group` on `port_groups`.`id` = `port_port_group`.`port_group_id` ").
			Where("`ports`.`id` = `port_port_group`.`port_id`").
			Where("port_groups.id IN (?)", req.PortGroupId))
	}

	totalQuery := query

	//统计数量综合总和
	err := totalQuery.Count(&total).Error
	if err != nil {
		return []Port{}, 0, err
	}

	var r []Port
	err = query.Preload("Protocols").Preload("Groups").Order("id desc").Scopes(PortPaginate(Page, Size)).Find(&r).Error
	if err != nil {
		return []Port{}, 0, err
	}

	data := make([]Port, 0, len(r))
	data = append(data, r...)
	return data, total, nil
}

func (d *defaultPortModel) GetIndexList(userId uint64) ([]Port, error) {
	query := d.DB.Table(d.table)
	if userId != 0 {
		query = query.Where("user_id = ?", userId)
	}
	var r []Port
	err := query.Select("id,port").Order("updated_at desc").Find(&r).Error
	if err != nil {
		return []Port{}, err
	}
	data := make([]Port, 0, len(r))
	data = append(data, r...)
	return data, nil
}

type PortChangeParam struct {
	Id           uint64
	UserId       uint64
	CompanyId    int64
	Ports        []uint64
	ProtocolIds  []uint64
	PortGroupIds []uint64
	Source       uint32
	CreatedAt    time.Time
}

func (d *defaultPortModel) PortStore(req *PortChangeParam) error {
	for _, port := range req.Ports {
		portObj := Port{}

		d.DB.Table(d.table).Where("user_id = ? AND port = ?", req.UserId, port).First(&portObj)
		if portObj.Id == 0 {
			portObj.Port = uint32(port)
			portObj.Source = SourceAdd

			if req.CreatedAt != (time.Time{}) {
				portObj.CreatedAt = req.CreatedAt
			} else {
				portObj.CreatedAt = time.Now()
			}
			portObj.UserId = req.UserId
			portObj.CompanyId = req.CompanyId
			d.DB.Table(d.table).Create(&portObj)
		}
		for _, protocol := range req.ProtocolIds {
			where := &PortPortProtocol{
				PortId:         portObj.Id,
				PortProtocolId: protocol,
			}
			update := &PortPortProtocol{
				PortId:         portObj.Id,
				PortProtocolId: protocol,
				UpdatedAt:      time.Now(),
			}

			d.DB.Table("port_port_protocol").Where(where).Assign(update).FirstOrCreate(&PortPortProtocol{})
		}
		for _, group := range req.PortGroupIds {
			where := &PortPortGroup{
				PortId:      portObj.Id,
				PortGroupId: group,
			}
			update := &PortPortGroup{
				PortId:      portObj.Id,
				PortGroupId: group,
				UpdatedAt:   time.Now(),
			}
			d.DB.Table("port_port_group").Where(where).Assign(update).FirstOrCreate(&PortPortGroup{})
		}
		where := &PortPortGroup{
			PortId:      portObj.Id,
			PortGroupId: DefaultGroupId,
		}
		update := &PortPortGroup{
			PortId:      portObj.Id,
			PortGroupId: DefaultGroupId,
			UpdatedAt:   time.Now(),
		}
		d.DB.Table("port_port_group").Where(where).Assign(update).FirstOrCreate(&PortPortGroup{})
	}
	return nil
}

func (d *defaultPortModel) PortDel(req *pb.PortDelRequest) error {

	var portIds []uint64
	if len(req.Ids) > 0 && req.Ids[0] != 0 {
		portIds = req.Ids
	} else {
		query := d.DB.Table(d.table).Where("user_id = ?", req.UserId)
		//if req.UserId != 0 {
		//	query = query.Where("user_id = ?", req.UserId)
		//}
		if req.Keyword != "" {
			query = query.Or("port LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))
		}

		if req.Keyword != "" {
			query = query.Where("EXISTS (?)", d.DB.Table("port_protocols").Joins("inner join `port_port_protocol` on `port_protocols`.`id` = `port_port_protocol`.`port_protocol_id` ").
				Where("`ports`.`id` = `port_port_protocol`.`port_id`").
				Where("protocol LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))).
				Where("EXISTS (?)", d.DB.Table("port_groups").Joins("inner join `port_port_group` on `port_groups`.`id` = `port_port_group`.`port_group_id` ").
					Where("`ports`.`id` = `port_port_group`.`port_id`").
					Where("name LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))).
				Or("port LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))

		}
		query.Select("id").Pluck("id", &portIds)
	}

	var builtInPorts []uint64
	d.DB.Table(d.table).Where("user_id = ? AND source = ?", req.UserId, StatusDefault).
		Pluck("id", &builtInPorts)

	ids := arrayDiff(portIds, builtInPorts)
	d.DB.Where("port_id IN (?)", ids).Delete(&PortPortProtocol{})
	d.DB.Where("port_id IN (?)", ids).Delete(&PortPortGroup{})
	d.DB.Where("user_id = ? AND id IN (?)", req.UserId, ids).Delete(&Port{})
	return nil
}

func (d *defaultPortModel) GetPortProtocolList(req *pb.PortProtocolIndexRequest) ([]PortProtocol, error) {
	query := d.DB.Table("port_protocols")
	var r []PortProtocol
	if req.Keyword != "" {
		query = query.Or("protocol LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))
	}
	err := query.Order("updated_at desc").Find(&r).Error
	if err != nil {
		return []PortProtocol{}, err
	}
	data := make([]PortProtocol, 0, len(r))
	data = append(data, r...)
	return data, nil
}

func (d *defaultPortModel) FindById(Id uint64, UserId uint64, WithDetail bool) (*Port, error) {
	var v Port
	query := d.DB.Table(d.table)

	if WithDetail {
		query = query.Preload("Protocols").Preload("Groups")
	}
	err := query.Where("user_id = ? and id = ?", UserId, Id).Order("id desc").Find(&v).Error
	return &v, err
}

func (d *defaultPortModel) PortEdit(req *PortChangeParam) error {

	d.DB.Table("port_port_group").Where("port_id", req.Id).Where("port_group_id != ?", DefaultGroupId).Delete(&PortPortGroup{})
	PortInfo, err := d.FindById(req.Id, req.UserId, false)
	if err != nil {
		return err
	}

	if PortInfo.CreatedAt != (time.Time{}) {
		req.CreatedAt = PortInfo.CreatedAt
	} else {
		req.CreatedAt = time.Now()
	}

	if PortInfo.Source >= 0 {
		req.Source = uint32(PortInfo.Source)
	} else {
		req.Source = SourceAdd
	}

	d.DB.Table("port_port_protocol").Where("port_id", req.Id).Delete(&PortPortProtocol{})

	err = d.PortStore(req)
	if err != nil {
		return err
	}
	return nil
}

func (d *defaultPortModel) PortUpdateStatus(req *pb.PortUpdateStatusRequest) error {

	query := d.DB.Table(d.table)

	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}

	if req.Keyword != "" {
		query = query.Where("EXISTS (?)", d.DB.Table("port_protocols").Joins("inner join `port_port_protocol` on `port_protocols`.`id` = `port_port_protocol`.`port_protocol_id` ").
			Where("`ports`.`id` = `port_port_protocol`.`port_id`").
			Where("protocol LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))).
			Where("EXISTS (?)", d.DB.Table("port_groups").Joins("inner join `port_port_group` on `port_groups`.`id` = `port_port_group`.`port_group_id` ").
				Where("`ports`.`id` = `port_port_group`.`port_id`").
				Where("name LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))).
			Or("port LIKE ?", fmt.Sprintf("%%%s%%", req.Keyword))

	}

	if len(req.ProtocolId) > 0 && req.ProtocolId[0] != 0 {
		query.Where("EXISTS (?)", d.DB.Table("port_protocols").Joins("inner join `port_port_protocol` on `port_protocols`.`id` = `port_port_protocol`.`port_protocol_id` ").
			Where("`ports`.`id` = `port_port_protocol`.`port_id`").
			Where("port_protocols.id IN (?)", req.ProtocolId))
	}

	if len(req.PortGroupId) > 0 && req.PortGroupId[0] != 0 {
		query = query.Where("EXISTS (?)", d.DB.Table("port_groups").Joins("inner join `port_port_group` on `port_groups`.`id` = `port_port_group`.`port_group_id` ").
			Where("`ports`.`id` = `port_port_group`.`port_id`").
			Where("port_groups.id IN (?)", req.PortGroupId))
	}

	if len(req.Ids) > 0 && req.Ids[0] != 0 {
		query = query.Where("id in (?)", req.Ids)
	}
	err := query.Preload("Protocols").Preload("Groups").Update("status", req.Status).Error
	if err != nil {
		return err
	}

	return nil
}

func PortPaginate(page, size int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		offset := (page - 1) * size
		if offset < 0 {
			offset = 0
		}
		return db.Offset(offset).Limit(size)
	}
}

func arrayDiff(a, b []uint64) []uint64 {
	m := map[uint64]bool{}
	for _, x := range b {
		m[x] = true
	}
	var diff []uint64
	for _, x := range a {
		if !m[x] {
			diff = append(diff, x)
		}
	}
	return diff
}

func (d *defaultPortModel) CountByIds(ids []uint64, status int32) int {
	var count int64
	err := d.DB.Table(d.table).Where("id IN (?) and status = ?", ids, status).Count(&count).Error
	if err != nil {
		return 0
	}
	return int(count)
}

func (d *defaultPortModel) GetByIds(ids []uint64, status int32) ([]*Port, error) {
	var r []*Port
	err := d.DB.Table(d.table).Where("id IN (?) and status = ?", ids, status).Find(&r).Error
	if err != nil {
		return []*Port{}, err
	}
	return r, nil
}

func (d *defaultPortModel) CountByUserIds(userId uint64, status int32) int {
	var count int64
	err := d.DB.Table(d.table).Where("user_id = ? and status = ?", userId, status).Count(&count).Error
	if err != nil {
		return 0
	}
	return int(count)
}

func (d *defaultPortModel) FindWithProtocolsByIDs(ids []uint64) ([]*Port, error) {
	// 查询ports表中id为ids的记录，并查询port_port_protocols表中port_id为ports表的id的记录，根据port_port_protocols表的port_protocol_id分组，返回port_protocols表的id、protocol、type字段
	var r []*Port
	err := d.DB.Table("ports").Where("id IN (?)", ids).Preload("Protocols").Find(&r).Error
	if err != nil {
		return []*Port{}, err
	}
	return r, nil
}

// FindProtocolById 根据协议ID查询协议信息
func (d *defaultPortModel) FindProtocolById(id uint64) (*PortProtocol, error) {
	var protocol PortProtocol
	err := d.DB.Table("port_protocols").Where("id = ?", id).First(&protocol).Error
	if err != nil {
		return nil, err
	}
	return &protocol, nil
}
