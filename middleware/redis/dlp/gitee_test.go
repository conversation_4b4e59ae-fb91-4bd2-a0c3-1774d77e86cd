package dlp

import (
	"context"
	"fmt"
	"testing"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_GetStrategy(t *testing.T) {
	initCfg()
	client := NewGiteeStrategyModel()
	strategy, err := client.GetStrategy(context.Background())
	fmt.Println(strategy)
	if err != nil {
		return
	}
}

func Test_SetStrategy(t *testing.T) {
	initCfg()
	client := NewGiteeStrategyModel()
	err := client.SetStrategy(context.Background(), "foradar_cache:test", 0)
	if err != nil {
		return
	}
	err = client.DelStrategy(context.Background())
	if err != nil {
		return
	}
}
