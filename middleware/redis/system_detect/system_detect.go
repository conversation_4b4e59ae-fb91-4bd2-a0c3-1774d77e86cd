package system_detect

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"

	redislib "micro-service/middleware/redis"
)

const (
	prefix         = "foradar_cache:business_system_detect:"
	ProgressPrefix = prefix + "progress:user_id:" // 进度缓存
	ProgressCmd    = "business_system_detect"
)

type SystemDetectModel interface {
	GetValue(ctx context.Context, key string) (string, error)
	SetValue(ctx context.Context, key, value string, dur ...time.Duration) error
}

type defaultSystemDetect struct {
	*redis.Client
}

func NewBusinessSystemModel() SystemDetectModel {
	return &defaultSystemDetect{
		Client: redislib.GetClient(),
	}
}

func (d *defaultSystemDetect) GetValue(ctx context.Context, key string) (string, error) {
	return d.Get(ctx, key).Result()
}

func (d *defaultSystemDetect) SetValue(ctx context.Context, key, value string, dur ...time.Duration) error {
	setDur := 6 * time.Hour
	if len(dur) > 0 {
		setDur = dur[0]
	}

	err := d.Client.Set(ctx, key, value, setDur).Err()
	return err
}
