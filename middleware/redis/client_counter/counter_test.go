package client_counter

import (
	"context"
	"fmt"
	"testing"
	"time"

	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"

	"github.com/stretchr/testify/assert"
)

func initCfg() {
	cfg.InitLoadCfg()
	_ = redis.GetInstance(cfg.LoadRedis())
}

func TestClientCounter_Counter(t *testing.T) {
	initCfg()

	client := NewClientCounterModel()
	for i := 0; i < 10; i++ {
		err := client.Counter(context.Background(), "DDV")
		assert.Equal(t, nil, err)
	}
}

func TestClientCounter_Keys(t *testing.T) {
	initCfg()

	list, err := NewClientCounterModel().Keys(context.TODO())
	assert.Nil(t, err)
	fmt.Println(list)
}

func Test_Set(t *testing.T) {
	initCfg()

	key := GenKey("foradar")
	for i := 0; i < 20; i++ {
		err := NewClientCounterModel().Set(context.Background(), key, "a")
		assert.Nil(t, err)
	}
}

func Test_MGet(t *testing.T) {
	initCfg()

	m, err := NewClientCounterModel().Get(context.Background(), "req_counter:2023-06:client_id:foradar")
	assert.Nil(t, err)
	for k, v := range m {
		fmt.Println(k, v)
	}

	err = redis.GetInstance().Expire(context.TODO(), "test_for_expire", time.Minute).Err()
	assert.Nil(t, err)
}
