linters-settings:
  forbidigo:
    # Forbid the following identifiers (list of regexp).
    # Default: ["^(fmt\\.Print(|f|ln)|print|println)$"]
    forbid:
      - ^print.*$
      - 'fmt\.Print.*'
      # Optionally put comments at the end of the regex, surrounded by `(# )?`
      # Escape any special characters.
      - 'fmt\.Print.*(# Do not commit print statements\.)?'
    # Exclude godoc examples from forbidigo checks.
    # Default: true
    # exclude_godoc_examples: false
  gofumpt:
    # Select the Go version to target.
    # Default: "1.15"
    # Deprecated: use the global `run.go` instead.
    lang-version: "1.18"
    # Module path which contains the source code being formatted.
    # Default: ""
    # Choose whether to use the extra rules.
    # Default: false
  govet:
    check-shadowing:
      true
      # enable:
    # - fieldalignment
    settings:
      printf:
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf
  golint:
    min-confidence: 0
  gocyclo:
    min-complexity: 32
  maligned:
    suggest-new: true
  dupl:
    threshold: 100
  goconst:
    min-len: 2
    min-occurrences: 2
  depguard:
    list-type: blacklist
    packages:
      # logging is allowed only by logutils.Log, logrus
      # is allowed to use only in logutils package
      - github.com/sirupsen/logrus
  misspell:
    locale: US
  lll:
    line-length: 200
  goimports:
    local-prefixes: git.gobies.org
  gocritic:
    enabled-tags:
      - performance
      - style
      - experimental
    disabled-checks:
      - wrapperFunc
      - yodaStyleExpr
      - unnamedResult
      # - dupImport # https://github.com/go-critic/go-critic/issues/845
      - commentedOutCode
      - importShadow
      - appendCombine
      - ifElseChain
      - typeAssertChain
      - builtinShadow
  gosec:
    excludes:
      - G404
      - G501
      - G401
      - G402
  revive:
    ignore-generated-header: true
    severity: warning
    rules:
      - name: atomic
      - name: line-length-limit
        severity: error
        arguments: [200]

linters:
  # enable:
  #   - errcheck
  #   - goimports
  #   - golint
  #   - govet
  #   - staticcheck
  #   - gocyclo
  #   - maligned
  #   - goconst
  #   - depguard
  #   - misspell
  #   - lll
  #   - gocritic
  disable-all: true
  ignore:
    - "unusual order of {a,b} params in comparison"
  enable:
    - gocritic
    - gocyclo
    - lll
    - goconst
    - misspell
    - govet
    - errcheck
    - forbidigo
    # - staticcheck
    - unused
    # - maligned
    - gosimple
    # - structcheck
    # - varcheck
    - ineffassign
    # - deadcode
    - typecheck
    # - golint
    - revive
    - gosec
    - unconvert
    # - goimports
    # - depguard
    - prealloc
    # - scopelint
    - whitespace
    - revive
    - nilnil

run:
  go: "1.20"

  # default concurrency is a available CPU number
  concurrency: 20

  # timeout for analysis, e.g. 30s, 5m, default is 1m
  deadline: 10m

  # exit code when at least one issue was found, default is 1
  issues-exit-code: 1
  # include test files or not, default is true
  tests: false
  skip-files: # 设置不需要检查的go源码文件，支持正则匹配，这里建议包括：_test.go
    - ".*\\.my\\.go$"
    - _test.go
  skip-dirs:
    - systemd
    # - third_party/(checker|passes)
golint:
  # minimal confidence for issues, default is 0.8
  min-confidence: 0.5
issues:
  exclude-use-default: true
  max-issues-per-linter: 10
  max-same-issues: 0
  # exclude:
  #   - EXC0002
# golangci.com configuration
# https://github.com/golangci/golangci/wiki/Configuration
# service:
#   golangci-lint-version: 1.17.x # use the fixed version to not introduce new linters unexpectedly
#   prepare:
#     - echo "here I can run custom commands, but no preparation needed for this repo"
