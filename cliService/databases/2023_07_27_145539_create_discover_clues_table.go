package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DiscoverCluesTable struct{}

func CreateDiscoverCluesTable() interfaces.Migration {
	return &DiscoverCluesTable{}
}

func (t *DiscoverCluesTable) Up() error {
	return mysql.Schema.Create("discover_clues", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 32).Unsigned().Index().Comment("任务ID")
		table.BigInt("clue_id", 32).Unsigned().Index().Comment("线索ID")
		table.BigInt("parent_id", 32).Unsigned().Index().Comment("父级线索")
		table.Text("path").Comment("线索路径")
		table.Timestamps()
	})
}

func (t *DiscoverCluesTable) Down() error {
	return mysql.Schema.DropIfExists("discover_clues")
}
