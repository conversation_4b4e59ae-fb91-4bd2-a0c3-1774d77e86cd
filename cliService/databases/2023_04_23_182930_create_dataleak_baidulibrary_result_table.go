package migrations

import (
	"micro-service/middleware/mysql/baidulibrary"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeakBaiduLibraryResultTable())
// }

type DataLeakBaiduLibraryResultTable struct{}

func CreateDataLeakBaiduLibraryResultTable() interfaces.Migration {
	return &DataLeakBaiduLibraryResultTable{}
}

func (*DataLeakBaiduLibraryResultTable) Up() error {
	return mysql.Schema.Create(baidulibrary.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("address", 255).Default("''").Comment("地址")
		table.String("url", 255).Default("''").Comment("地址")
		table.String("title", 2000).Default("''").Comment("标题")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (*DataLeakBaiduLibraryResultTable) Down() error {
	return mysql.Schema.DropIfExists(baidulibrary.ResultTableName)
}
