package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateGeneralHistoryTable())
// }

type GeneralHistoryTable struct{}

func CreateGeneralHistoryTable() interfaces.Migration {
	return &GeneralHistoryTable{}
}

func (t *GeneralHistoryTable) Up() error {
	return mysql.Schema.Create("general_history", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("keyword", 512).Index().Comment("搜索关键字")
		table.Integer("process", 11).Comment("扩展进度")
		table.Boolean("type").Index().Comment("类型")
		table.TableComment("公共线索库扩展历史")
		table.Timestamps()
	})
}

func (t *GeneralHistoryTable) Down() error {
	return mysql.Schema.DropIfExists("general_history")
}
