package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceThreatTable struct{}

func CreateIntelligenceThreatTable() interfaces.Migration {
	return &IntelligenceThreatTable{}
}

func (t *IntelligenceThreatTable) Up() error {
	return mysql.Schema.Create("intelligence_threat", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("url", 255).Index().Comment("风险URL")
		table.String("ip", 128).Index().Nullable().Comment("IP地址")
		table.String("domain", 200).Index().Nullable().Comment("域名")
		table.String("type", 512).Nullable().Comment("风险类型")
		table.String("tags", 512).Nullable().Comment("标签")
		table.String("country", 512).Nullable().Comment("国家")
		table.Integer("status", 11).Default(1).Nullable().Comment("是否在线: 1/在线 2/离线")
		table.String("source", 128).Nullable().Comment("数据来源")
		table.DateTime("found_at").Index().Nullable().Comment("发现时间")
		table.Timestamps()
		table.Comment("情报-威胁情报")
	})
}

func (t *IntelligenceThreatTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_threat")
}
