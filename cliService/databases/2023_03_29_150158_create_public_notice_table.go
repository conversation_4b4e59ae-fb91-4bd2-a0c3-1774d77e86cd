package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreatePublicNoticeTable())
// }

type PublicNoticeTable struct{}

func CreatePublicNoticeTable() interfaces.Migration {
	return &PublicNoticeTable{}
}

func (t *PublicNoticeTable) Up() error {
	return mysql.Schema.Create("public_notice", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.LongText("notice").Comment("公告")
		table.DateTime("up_at_start").Comment("开始时间").Nullable().Index()
		table.DateTime("up_at_end").Comment("结束时间").Nullable().Index()
		table.TableComment("公告表")
		table.Timestamps()
		table.DeletedAt(true)
	})
}

func (t *PublicNoticeTable) Down() error {
	return mysql.Schema.DropIfExists("public_notice")
}
