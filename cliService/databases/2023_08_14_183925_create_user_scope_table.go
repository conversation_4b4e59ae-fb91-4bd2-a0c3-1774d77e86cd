package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type UserScopeTable struct{}

func CreateUserScopeTable() interfaces.Migration {
	return &UserScopeTable{}
}

func (t *UserScopeTable) Up() error {
	return mysql.Schema.Create("user_scope", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("user_id", 22).Comment("用户ID").Index()
		table.String("scope", 512).Comment("权限").Index()
		table.Timestamps()
	})
}

func (t *UserScopeTable) Down() error {
	return mysql.Schema.DropIfExists("user_scope")
}
