package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/auth_access_client"
)

type AlterAuthAccessClient20230805 struct{}

func AlterAuthAccessClientTable20230805() interfaces.Migration {
	return &AlterAuthAccessClient20230805{}
}

func (t *AlterAuthAccessClient20230805) Up() error {
	return mysql.Schema.Table(auth_access_client.AuthAccessClientTable, func(table interfaces.Blueprint) {
		table.Integer("day_client_task_limit", 10).Default(0).Comment("客户端每日任务限额")
		table.Integer("client_task_total", 10).Default(0).Comment("客户端任务累计限额")
	})
}

func (t *AlterAuthAccessClient20230805) Down() error {
	return mysql.Schema.Table(auth_access_client.AuthAccessClientTable, func(table interfaces.Blueprint) {
		table.DropColumn("day_client_task_limit")
		table.DropColumn("client_task_total")
	})
}
