package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	magicalsearch "micro-service/middleware/mysql/dataleak_magicalsearch"
)

type DataLeakMagicalsearchRelationTable struct{}

func CreateDataLeakMagicalsearchRelationTable() interfaces.Migration {
	return &DataLeakMagicalsearchRelationTable{}
}

func (t *DataLeakMagicalsearchRelationTable) Up() error {
	return mysql.Schema.Create(magicalsearch.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.Timestamps()
	})
}

func (t *DataLeakMagicalsearchRelationTable) Down() error {
	return mysql.Schema.DropIfExists(magicalsearch.RelationTableName)
}
