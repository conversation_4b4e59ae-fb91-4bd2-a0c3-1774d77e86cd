package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	magicalsearch "micro-service/middleware/mysql/dataleak_magicalsearch"
)

type DataLeakMagicalsearchResultTable struct{}

func CreateDataLeakMagicalsearchResultTable() interfaces.Migration {
	return &DataLeakMagicalsearchResultTable{}
}

func (t *DataLeakMagicalsearchResultTable) Up() error {
	return mysql.Schema.Create(magicalsearch.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("file_url", 500).Default("''").Comment("地址")
		table.String("file_name", 500).Default("''").Comment("文件名")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (t *DataLeakMagicalsearchResultTable) Down() error {
	return mysql.Schema.DropIfExists(magicalsearch.ResultTableName)
}
