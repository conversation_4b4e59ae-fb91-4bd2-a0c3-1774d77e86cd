package migrations

import (
	"micro-service/middleware/mysql/official_account"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, AlterOfficialAccountHistoryTable20230714())
// }

type OfficialAccountHistory20230714 struct{}

func AlterOfficialAccountHistoryTable20230714() interfaces.Migration {
	return &OfficialAccountHistory20230714{}
}

func (*OfficialAccountHistory20230714) Up() error {
	return mysql.Schema.Table(official_account.HistoryTable, func(table interfaces.Blueprint) {
		table.Decimal("progress", 10, 2).Default(0).Comment("进度")
	})
}

func (*OfficialAccountHistory20230714) Down() error {
	return mysql.Schema.Table(official_account.HistoryTable, func(table interfaces.Blueprint) {
		table.DropColumn("progress")
	})
}
