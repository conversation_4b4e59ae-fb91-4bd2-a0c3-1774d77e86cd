package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/official_account"
)

type AlterOfficalAccountHistory20231030 struct{}

func CreateAlterOfficalAccountHistory20231030() interfaces.Migration {
	return &AlterOfficalAccountHistory20231030{}
}

func (t *AlterOfficalAccountHistory20231030) Up() error {
	return mysql.Schema.Table(official_account.HistoryTable, func(table interfaces.Blueprint) {
		table.Index("keyword").IndexName("idx_keyword")
	})
}

func (t *AlterOfficalAccountHistory20231030) Down() error {
	return mysql.Schema.Table(official_account.HistoryTable, func(table interfaces.Blueprint) {
		table.DropIndex("idx_keyword")
	})
}
