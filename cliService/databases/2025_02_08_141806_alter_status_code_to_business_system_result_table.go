package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterStatusCodeToBusinessSystemResultTable struct{}

func CreateAlterStatusCodeToBusinessSystemResultTable() interfaces.Migration {
	return &AlterStatusCodeToBusinessSystemResultTable{}
}

func (t *AlterStatusCodeToBusinessSystemResultTable) Up() error {
	return mysql.Schema.Table("business_system_result", func(table interfaces.Blueprint) {
		table.DateTime("last_live_at").Index().Nullable().Comment("最后发现在线时间")
		table.String("status_code", 255).Comment("url返回的状态码").Nullable()
		table.Integer("status", 1).Index().Default(0).Comment("url业务系统资产的状态 1/确认 2/忽略 0/待确认").Nullable()
	})
}

func (t *AlterStatusCodeToBusinessSystemResultTable) Down() error {
	return nil
}
