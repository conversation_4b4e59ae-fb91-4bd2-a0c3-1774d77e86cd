{"apifoxCli": "1.2.21", "item": [{"item": [{"id": "b4ba75b7-565f-47a4-8c27-d1a9248f65e9", "type": "group", "metaInfo": {"id": "b4ba75b7-565f-47a4-8c27-d1a9248f65e9", "type": "group", "scopeType": "start", "scopeEndId": "45ae1eed-23a6-4ef9-9d66-e60612c4ba59", "name": "root", "onError": "end"}}, {"id": "f56f97c5-4663-45e0-ae77-018682ba2fdb", "name": "添加任务 masscan(1个tcp 存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:80?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个tcp 存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(true);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108410969, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个tcp 存活端口", "id": "3609dbad-d6c1-4233-b3a0-072469278d24", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "a5f2e4a9-b1d4-4121-a00b-e6d028a85fbf", "name": "添加任务 masscan(1个tcp不存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\":\"300\",\n    \"blacklist\":\"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\":false,\n    \"crawler_specific_url\":\"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\":\"\",\n    \"deep_get_mac\":false,\n    \"deep_get_os\":false,\n    \"gateway_mac\":\"false\",\n    \"grab_concurrent\":10,\n    \"hostinfos\":[],\n    \"ip_list\":\"**********36\",\n    \"ip_list_filename\":\"\",\n    \"is_ipv6\":false,\n    \"max_asset_num\":10000, \n    \"ping_scan\":false,\n    \"ports\":\"7,11,13,17,19,21,22,23,25,26,37,43,49,53,67,70,79,81,82,83,84,88,102,104,110,111,113,119,135,138,139,143,175,179,199,264,389,444,445,465,502,503,512,515,523,548,554,564,587,631,636,646,771,789,873,880,902,992,993,995,1025,1026,1027,1080,1099,1177,1194,1200,1201,1234,1241,1260,1311,1344,1433,1471,1505,1521,1723,1863,1883,1911,1962,1991,2000,2001,2049,2080,2082,2083,2086,2087,2121,2181,2222,2323,2332,2375,2376,2379,2401,2404,2424,2455,2480,2501,2628,3000,3128,3260,3288,3299,3306,3307,3310,3333,3388,3389,3390,3460,3541,3542,3689,3690,3749,3780,4000,4022,4040,4063,4064,4369,4443,4444,4567,4712,4730,4786,4840,4848,4880,4911,4949,5000,5001,5006,5007,5009,5050,5084,5222,5269,5357,5400,5432,5555,5560,5577,5672,5678,5900,5901,5938,5984,5985,5986,6001,6068,6379,6488,6664,6665,6666,6667,6668,6669,7000,7001,7071,7077,7288,7474,7547,7548,7634,7777,7779,7911,8000,8001,8008,8009,8010,8020,8025,8030,8040,8060,8069,8080,8081,8086,8087,8089,8090,8098,8099,8112,8125,8126,8139,8161,8200,8291,8333,8334,8377,8378,8443,8500,8545,8554,8649,8686,8800,8834,8880,8888,8889,8983,9000,9001,9003,9010,9042,9051,9080,9090,9092,9093,9100,9151,9191,9200,9333,9418,9443,9527,9530,9595,9653,9700,9711,9944,9981,9999,10000,10001,10162,10243,10333,11001,11211,11300,11310,12345,13579,14000,14147,14265,16010,16030,16992,16993,18001,18081,18245,20000,20547,20880,22105,22222,23023,23424,25000,25105,25565,27015,27017,28017,32400,33338,37777,41795,45554,49151,49152,49153,49154,49155,50000,50050,50070,50100,51106,55553,60010,60030,61613,61616,62078,89,80,6000,443,59110,T:3308:exec,T:3308:iec61850,T:3308:synchrophasor,T:3311:https,T:3311:git,T:3311:ssh,T:3311:ftp,T:3311:mysql,T:3312:https,T:3312:git,T:3312:ssh,T:3312:ftp,T:3312:mysql,T:3313:https,T:3313:git,T:3313:ssh,T:3313:ftp,T:3313:mysql,T:498:exec,T:498:9p,T:59855:synchrophasor,T:59855:android-debug-bridge,T:59855:iec61850,T:45198:hbase_region_server(http),T:45198:hbase_master(http),T:3309:https,T:3309:git,T:3309:ssh,T:3309:ftp,T:3309:mysql,T:8082:http,T:8082:https,T:1245:cassandra,T:1245:iec61850,T:6589:cassandra,T:6589:iec61850,T:5587:cassandra,T:5587:iec61850,U:53,U:67,U:69,U:113,U:123,U:137,U:161,U:162,U:391,U:500,U:520,U:623,U:626,U:705,U:1027,U:1194,U:1434,U:1604,U:1645,U:1701,U:1812,U:1900,U:1967,U:1993,U:2094,U:2123,U:2152,U:2424,U:2425,U:2427,U:3283,U:3333,U:3671,U:3702,U:3784,U:4070,U:4500,U:4800,U:5006,U:5050,U:5060,U:5094,U:5351,U:5353,U:5554,U:5632,U:5683,U:6881,U:6969,U:8888,U:9600,U:10001,U:17185,U:20000,U:28784,U:30310,U:30311,U:30312,U:30313,U:30718,U:32768,U:34962,U:34964,U:44818,U:47808,U:48899,U:80,U:443,U:59110\",\n    \n    \"protocol_update_cycle\":0,\n    \"repeat_times\":0,\n    \"resume_filename\":\"\",\n    \"send_eth\":\"\",\n    \"task_id\": \"{% mock 'string' , '0123456789' , 5 , 10 %}\",\n    \"task_type\":\"quick\",\n    \"treck_scan\":false,\n    \"unknown_protocol_in_db\":false\n}\n", "generateMode": "request", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:81?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个tcp不存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(false);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108431071, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个tcp不存活端口", "id": "bb7c5a6a-e67e-4e0f-bcf5-1a95cd266bba", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "d4af6507-58bf-4295-8d8f-dabf8834f38a", "name": "添加任务 masscan(多个tcp存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\":\"100\",\n    \"blacklist\":\"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\":false,\n    \"crawler_specific_url\":\"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\":\"\",\n    \"deep_get_mac\":false,\n    \"deep_get_os\":false,\n    \"gateway_mac\":\"false\",\n    \"grab_concurrent\":10,\n    \"hostinfos\":[],\n    \"ip_list\":\"************\",\n    \"ip_list_filename\":\"\",\n    \"is_ipv6\":false,\n    \"max_asset_num\":10000, \n    \"ping_scan\":false,\n    \"ports\":\"22,80,443\",\n    \n    \"protocol_update_cycle\":0,\n    \"repeat_times\":0,\n    \"resume_filename\":\"\",\n    \"send_eth\":\"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\":\"quick\",\n    \"treck_scan\":false,\n    \"unknown_protocol_in_db\":false\n}\n", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "const queryRequest = {", "  url: elastic + \"/fofaee_service/service/_search\",", "  method: \"POST\",", "  header: {", "    \"Content-Type\": \"application/json\"", "  },", "  body: {", "    mode: 'raw',", "    raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"ip\": \"************\" } }, { \"terms\": { \"port\": [22, 80, 443] } }] } } }),", "  }", "}", "", "let timer = setInterval(() => {", "  query();", "}, 5000);", "", "function clearTimer() {", "  clearInterval(timer)", "  timer = null", "}", "", "function assert() {", "  pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json())", "    pm.test(\"多个tcp存活端口\", function () {", "      pm.response.to.not.be.error;", "      pm.expect(response.json().hits.total).to.be.above(2);", "    });", "  });", "}", "", "function query() {", "  pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "    console.log(response.json())", "    if (response.json().data.state == \"5\") {", "      clearTimer()", "      assert()", "    }", "", "    if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "      clearTimer()", "    }", "", "    console.log(\"等待任务结束：\", response.json())", "  });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108447375, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个tcp存活端口", "id": "2e806da2-5ba7-4436-bcec-3a6306bfa0a7", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "62b502b5-5e7d-48a6-9881-2a93cea319ee", "name": "添加任务 masscan(多个tcp端口全部不存活)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"21,81,444\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "  url: elastic + \"/fofaee_service/service/_search\",", "  method: \"POST\",", "  header: {", "    \"Content-Type\": \"application/json\"", "  },", "  body: {", "    mode: 'raw',", "    raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"ip\": \"************\" } }, { \"terms\": { \"port\": [21, 81, 444] } }] } } }),", "  }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "  query();", "}, 5000);", "", "function clearTimer() {", "  clearInterval(timer)", "  timer = null", "}", "", "function assert() {", "  pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json())", "    pm.test(\"多个tcp端口全部不存活\", function () {", "      pm.response.to.not.be.error;", "      pm.expect(response.json().hits.total).to.be.equal(0);", "    });", "  });", "}", "", "function query() {", "  pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "    console.log(response.json())", "    if (response.json().data.state == \"5\") {", "      clearTimer()", "      assert()", "    }", "", "    if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "      clearTimer()", "    }", "", "    console.log(\"等待任务结束：\", response.json())", "  });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108482469, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个tcp端口全部不存活", "id": "c33b13a4-9392-43be-a47a-1786e9c94e17", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "e428695e-3970-466b-a247-937166c5a77a", "name": "添加任务 masscan(1个udp 存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:111\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:111?pretty\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"1个udp存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.equal(true);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108524804, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个udp 存活端口", "id": "5e3b82b1-3d1d-4caa-824c-b763d4f80e53", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "840391d8-7c15-4021-8cea-66e2d5c53beb", "name": "添加任务 masscan(1个udp 不存活端口 )", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:112\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/************:112?pretty\", function (err, response) {", "    console.log(response.json())", "    pm.test(\"1个udp 不存活端口\", function () {", "        pm.response.to.not.be.error;", "        pm.expect(response.json().found).to.equal(false);", "    });", "});", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108530883, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "1个udp 不存活端口 ", "id": "ec9cd295-6fbe-43ab-b548-65496fbf3164", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "f0ec9410-a01c-46ac-b730-88e2bec11aa7", "name": "添加任务 masscan(多个udp存活端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:111,U:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"base_protocol\": \"udp\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"多个udp存活端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108531331, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个udp存活端口", "id": "d084028d-204b-4de7-be84-0168926b502b", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "08b96629-7514-4fba-a36a-dc033ed3ae43", "name": "添加任务 masscan(多个udp端口全部不存活)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"U:5060,U:1194,U:80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"base_protocol\": \"udp\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"多个udp端口全部不存活\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108614342, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "多个udp端口全部不存活", "id": "f918c818-439a-4850-ae73-e6ae767d2571", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "a4f89869-4658-47a7-a29a-3345d503d36d", "name": "添加任务 masscan(tcp、udp端口混扫，至少各存活一个)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,443,22,U:111,U:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"aggs\": { \"port_terms\": { \"terms\": { \"field\": \"port\", \"size\": 6 } } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"tcp、udp端口混扫，至少各存活一个\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().aggregations.port_terms.buckets.length).to.be.above(3);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108623413, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "tcp、udp端口混扫，至少各存活一个", "id": "a0812435-64f8-40de-9c0b-3bea8d788797", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "34769a84-f709-4590-a3c1-115bdeba80a6", "name": "添加任务 masscan(错误端口)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"http,22,S:111,U:mysql:53,U:161\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_count\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"错误端口\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108641819, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "错误端口", "id": "82e5654c-089c-4f34-bb40-b34520e65849", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "dfa00891-d7c8-4399-8082-dcc0d8ad992c", "name": "添加任务 masscan(发包速率小于0)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"-100000000\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 269072108, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-08T08:51:46.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108726922, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "发包速率小于0", "id": "3bc4881d-4d39-497b-87e7-7b6194ed5419", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "e99b46d9-bcee-479c-86ba-e6da5b547d39", "name": "添加任务 masscan(发包速率超出整数范围)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"9999999999999999999999999999999\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 269072108, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-08T08:51:46.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108744358, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "发包速率超出整数范围", "id": "880a5405-098d-4fb3-99fa-b4a56ea64f2d", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "f5fe7ebe-4bb9-4974-a785-3049fe5986f6", "name": "添加任务 masscan(扫描目标全是黑名单ip)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108744632, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "扫描目标全是黑名单ip", "id": "28f24f29-ba94-4c67-a786-106879c13dea", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "fe7d8058-bbd4-4dec-9319-d4932cdef25b", "name": "添加任务 masscan(扫描目标不全是黑名单ip)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标不全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108745562, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "扫描目标不全是黑名单ip", "id": "fc0e4a85-e58b-40fb-9f9e-561c2e68ab50", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "335dc8ca-7f4a-4056-9e5b-1864788ab22c", "name": "添加任务 masscan(黑名单ip不是有效的ipv4)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"111***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 269072108, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-08T08:51:46.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108746132, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "黑名单ip不是有效的ipv4", "id": "7431cb02-4630-4c95-a844-9111ac46c41a", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "8427073e-e684-46da-be0d-de31d742448e", "name": "添加任务 masscan(ipv4扫描黑名单ip是ipv6)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"2001:4b98:e01::38,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"ipv4扫描黑名单ip是ipv6\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108758095, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "ipv4扫描黑名单ip是ipv6", "id": "b87682ad-6ba3-4fd9-a855-a69a16a66315", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "bb947a0f-bf08-4a75-85e9-6f8882449673", "name": "添加任务 masscan(黑名单ip不是有效的ipv6)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"2001aaaa:4b98:e01::38,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": true,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 269072108, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-08T08:51:46.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108747658, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "黑名单ip不是有效的ipv6", "id": "dbcc444b-2929-4ff9-ab3a-63f3071a2e6b", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "844107e9-5dc0-483c-8a71-ea2327a10199", "name": "添加任务 masscan(masscan ipv6扫描)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************,***********\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": true,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,22,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}], "responseDefinition": {"id": 269072108, "name": "参数错误", "code": 422, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}}, "defaultEnable": true, "projectId": 3260764, "ordering": 2, "createdAt": "2023-09-08T08:51:46.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108758950, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "masscan ipv6扫描", "id": "3dddcf19-d090-4649-9af7-a01e117aed0c", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "9935668c-2fbe-4f99-9cce-a59628b736cd", "name": "添加任务 masscan(全端口扫描)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"0-65535,U:53,67,69,80,88,113,123,137,138,161,162,391,427,443,500,520,623,626,705,853,1027,1194,1434,1604,1645,1701,1812,1900,1967,1993,2083,2094,2123,2152,2424,2425,2427,3283,3333,3391,3478,3671,3702,3784,4050,4070,4500,4800,5000,5001,5002,5004,5005,5006,5007,5008,5050,5060,5061,5093,5094,5095,5351,5353,5554,5632,5673,5683,6002,6003,6006,6060,6881,6969,7000,7001,7003,7005,8002,8888,9000,9100,9600,10001,17185,20000,28784,30310,30311,30312,30313,30718,32768,34962,34963,34964,44818,47808,48899,59110\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"扫描目标不全是黑名单ip\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.above(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108765298, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全端口扫描", "id": "23361928-4a94-45cb-a613-74f9978ad4f7", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "ab0c1097-40b1-4351-bb90-2e8cb6344a1c", "name": "添加任务 masscan(全端口扫描端口不正常 -1-65537)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"-1-65537,U:53,67,69,80,88,113,123,137,138,161,162,391,427,443,500,520,623,626,705,853,1027,1194,1434,1604,1645,1701,1812,1900,1967,1993,2083,2094,2123,2152,2424,2425,2427,3283,3333,3391,3478,3671,3702,3784,4050,4070,4500,4800,5000,5001,5002,5004,5005,5006,5007,5008,5050,5060,5061,5093,5094,5095,5351,5353,5554,5632,5673,5683,6002,6003,6006,6060,6881,6969,7000,7001,7003,7005,8002,8888,9000,9100,9600,10001,17185,20000,28784,30310,30311,30312,30313,30718,32768,34962,34963,34964,44818,47808,48899,59110\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_service/service/_count\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"全端口扫描，输入端口不正常 -1-65537\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().count).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108772289, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全端口扫描端口不正常 -1-65537", "id": "3e688b2e-17fb-49b0-a63a-344ae8835fa4", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "be9fdb11-5984-4de2-8c03-ee6ce6c2c8e6", "name": "添加任务 masscan(全协议识别，开关关闭，扫描非默认端口协议)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\" } }] } } }),", "    }", "}", "", "", "// 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"全协议识别，开关关闭，扫描非默认端口协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108773829, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全协议识别，开关关闭，扫描非默认端口协议", "id": "b1e91789-5d2d-4b15-828a-2a55280f20df", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "de07e3a4-fd67-42e8-89a1-a8f93ea233dc", "name": "添加任务 masscan(全协议识别，开关打开，扫描非默认端口协议2179 etcd)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"terms\": { \"protocol\": [\"etcd\", \"http\"] } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"全协议识别，开关关闭，扫描非默认端口协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108780892, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "全协议识别，开关打开，扫描非默认端口协议2179 etcd", "id": "64120cdc-85de-49df-a29f-e96278be070f", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "2765bfa0-2714-4504-a6ac-65dc2af2a0bf", "name": "添加任务 masscan(指定端口扫描2179 etcd)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"T:2179:etcd\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\", } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"指定端口扫描2179 etcd\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(1);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108781061, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "指定端口扫描2179 etcd", "id": "357340d9-61d0-4f2d-b25a-60e18111ed7c", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "b876f503-588d-4758-9211-177e38367e97", "name": "添加任务 masscan(指定端口扫描2179 etcd  不指定协议)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"**********11\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"2179\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.delay", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        setTimeout(()=>{}, 8000);", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.1.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "", "const queryRequest = {", "    url: elastic + \"/fofaee_service/service/_search\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"bool\": { \"must\": [{ \"term\": { \"protocol\": \"etcd\" } }] } } }),", "    }", "}", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(queryRequest, function (err, response) {", "        console.log(response.json())", "        pm.test(\"指定端口扫描2179 etcd  不指定协议\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().hits.total).to.be.equal(0);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108783232, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "指定端口扫描2179 etcd  不指定协议", "id": "a76eb3ac-8dc4-4533-9be5-886cec09d432", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "ba004573-9330-4465-9b83-58a478811c8f", "name": "添加任务 masscan(域名扫描，不指定ip域名关系)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_list\": \"*************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80,443\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_subdomain/subdomain/*************\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"域名扫描，不指定ip域名关系\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.be.equal(true);", "            pm.expect(response.json()._source.status_code).to.be.equal(403);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108781443, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "域名扫描，不指定ip域名关系", "id": "3793a64e-404a-4e2f-9a72-c2a3d9a24a76", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "922c6fd7-8bc1-4177-8616-1e4497192f70", "name": "添加任务 masscan(域名扫描，指定ip域名关系)", "request": {"url": {"protocol": "http", "port": "61234", "path": ["api", "v1", "tasks", "add"], "host": ["10", "10", "11", "212"], "query": [], "variable": []}, "header": [{"key": "User-Agent", "value": "Apifox/1.0.0 (https://apifox.com)"}, {"key": "Content-Type", "value": "application/json"}], "method": "POST", "baseUrl": "http://************:61234", "body": {"mode": "raw", "raw": "{\n    \"bandwidth\": \"100\",\n    \"blacklist\": \"***********,*********/32,*********/32,**********/32,**********4/32,**********/32,**********/32,**********/32,**********/32,***********,**********,**********,**********00,**********00,***********,***********,**********2,**********0,**********,**********,***********,***********,**********,**********,**********4,**********,*********,*********\",\n    \"crawler_all_url\": false,\n    \"crawler_specific_url\": \"/#/login|/druid/basic.json|/containers\",\n    \"crawler_url_black_key\": \"\",\n    \"deep_get_mac\": false,\n    \"deep_get_os\": false,\n    \"gateway_mac\": \"false\",\n    \"grab_concurrent\": 10,\n    \"hostinfos\": [],\n    \"ip_domain_relations\": {\n        \"*************\": [\n            \"bbs.1cool.vip\"\n        ]\n    },\n    \"ip_list\": \"*************\",\n    \"ip_list_filename\": \"\",\n    \"is_ipv6\": false,\n    \"max_asset_num\": 10000,\n    \"ping_scan\": false,\n    \"ports\": \"80\",\n    \"protocol_update_cycle\": 0,\n    \"repeat_times\": 0,\n    \"resume_filename\": \"\",\n    \"send_eth\": \"\",\n    \"task_id\": \"{% mock 'increment' , 5 %}\",\n    \"task_type\": \"quick\",\n    \"treck_scan\": false,\n    \"unknown_protocol_in_db\": false\n}", "generateMode": "normal", "type": "application/json"}, "type": "http"}, "response": [], "event": [{"listen": "prerequest", "script": {"id": "preProcessors.0.commonScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "const queryRequest = {", "    url: elastic + \"/fofaee_service,fofaee_subdomain,fofaee_task_assets/_delete_by_query?pretty\",", "    method: \"POST\",", "    header: {", "        \"Content-Type\": \"application/json\"", "    },", "    body: {", "        mode: 'raw',", "        raw: JSON.stringify({ \"query\": { \"match_all\": {} } }),", "    }", "}", "pm.sendRequest(queryRequest, function (err, response) {", "    console.log(response.json());", "});", "      "]}}, {"listen": "test", "script": {"id": "postProcessors.0.customScript", "type": "text/javascript", "exec": ["", "        if (JSON.setEnableBigInt) {", "          JSON.setEnableBigInt(undefined);", "        }", "        // 获取环境变量", "var elastic = pm.environment.get('elastic');", "", "var task_sate = pm.environment.get('task_state');", "var body = pm.request.body.toJSON();", "var bodyStr = body.raw;", "var bodyJSON = JSON.parse(bodyStr);", "", "let timer = setInterval(() => {", "    query();", "}, 5000);", "", "function clearTimer() {", "    clearInterval(timer)", "    timer = null", "}", "", "function assert() {", "    pm.sendRequest(elastic + \"/fofaee_subdomain/subdomain/bbs.1cool.vip\", function (err, response) {", "        console.log(response.json())", "        pm.test(\"域名扫描，指定ip域名关系\", function () {", "            pm.response.to.not.be.error;", "            pm.expect(response.json().found).to.be.equal(true);", "            pm.expect(response.json()._source.status_code).to.be.equal(200);", "        });", "    });", "}", "", "function query() {", "    pm.sendRequest(task_sate + \"/api/v1/tasks/\" + bodyJSON.task_id + \"/state\", function (err, response) {", "        console.log(response.json())", "        if (response.json().data.state == \"5\") {", "            clearTimer()", "            assert()", "        }", "", "        if (response.json().data.state == \"4\" || response.json().data.state == \"6\") {", "            clearTimer()", "        }", "", "        console.log(\"等待任务结束：\", response.json())", "    });", "}", "      "]}}], "responseDefinition": {"id": 268085895, "name": "成功", "code": 200, "contentType": "json", "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "defaultEnable": true, "projectId": 0, "ordering": 1, "createdAt": "2023-09-07T06:19:19.000Z", "updatedAt": "2023-09-08T08:51:46.000Z", "deletedAt": null, "apiDetailId": 108873763, "schemaDefinitions": {}}, "requestDefinition": {"jsonSchema": {"type": "object", "properties": {"bandwidth": {"type": "string"}, "blacklist": {"type": "string"}, "crawler_all_url": {"type": "boolean"}, "crawler_specific_url": {"type": "string"}, "crawler_url_black_key": {"type": "string"}, "deep_get_mac": {"type": "boolean"}, "deep_get_os": {"type": "boolean"}, "gateway_mac": {"type": "string"}, "grab_concurrent": {"type": "integer"}, "hostinfos": {"type": "array", "items": {"type": "string"}}, "ip_list": {"type": "string"}, "ip_list_filename": {"type": "string"}, "is_ipv6": {"type": "boolean"}, "max_asset_num": {"type": "integer"}, "ping_scan": {"type": "boolean"}, "ports": {"type": "string"}, "protocol_update_cycle": {"type": "integer"}, "repeat_times": {"type": "integer"}, "resume_filename": {"type": "string"}, "send_eth": {"type": "string"}, "task_id": {"type": "string", "mock": {"mock": "@increment(100)"}}, "task_type": {"type": "string"}, "treck_scan": {"type": "boolean"}, "unknown_protocol_in_db": {"type": "boolean"}}, "required": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"], "x-apifox-orders": ["bandwidth", "blacklist", "crawler_all_url", "crawler_specific_url", "crawler_url_black_key", "deep_get_mac", "deep_get_os", "gateway_mac", "grab_concurrent", "hostinfos", "ip_list", "ip_list_filename", "is_ipv6", "max_asset_num", "ping_scan", "ports", "protocol_update_cycle", "repeat_times", "resume_filename", "send_eth", "task_id", "task_type", "treck_scan", "unknown_protocol_in_db"]}}, "metaInfo": {"httpApiId": 108873763, "httpApiCaseId": 108783647, "httpApiName": "添加任务 masscan", "httpApiPath": "/api/v1/tasks/add", "httpApiMethod": "post", "httpApiCaseName": "域名扫描，指定ip域名关系", "id": "e05240d8-796b-41f0-93b2-18b734506389", "type": "http"}, "type": "http", "protocolProfileBehavior": {"useWhatWGUrlParser": false, "disableUrlEncoding": false}}, {"id": "45ae1eed-23a6-4ef9-9d66-e60612c4ba59", "type": "group", "metaInfo": {"id": "45ae1eed-23a6-4ef9-9d66-e60612c4ba59", "type": "group", "scopeType": "end", "scopeStartId": "b4ba75b7-565f-47a4-8c27-d1a9248f65e9"}}], "name": "masscan"}], "info": {"name": "masscan"}, "dataSchemas": {}, "mockRules": {"rules": [], "enableSystemRule": true}, "environment": {"id": 14091549, "name": "测试环境", "baseUrl": "http://************:61234", "baseUrls": {"default": "http://************:61234"}, "variable": {"id": "94e11772-df8b-474a-97bc-52b92bd17560", "name": "测试环境", "values": [{"type": "any", "value": "", "key": "elastic", "isBindInitial": true, "initialValue": "http://************:9200"}, {"type": "any", "value": "", "key": "task_state", "isBindInitial": true, "initialValue": "http://************:6789"}]}, "type": "normal", "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "globals": {"baseUrl": "", "baseUrls": {}, "variable": {"id": "eeefe1b2-1b7b-498b-962c-db0e8cb373c6", "values": []}, "parameter": {"header": [], "query": [], "body": [], "cookie": []}}, "isServerBuild": false, "isTestFlowControl": true}