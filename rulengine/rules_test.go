package rulengine

import (
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRuleManager_Size(t *testing.T) {
	rm := &ruleManager{}
	assert.Equal(t, 0, rm.<PERSON><PERSON>())
	assert.True(t, rm.Add(`a`, `banner=b`))
	assert.Equal(t, 1, rm.<PERSON><PERSON>())
	assert.False(t, rm.Add(`a`, `banner=b`))
	assert.Equal(t, 1, rm.<PERSON>ze())
	assert.True(t, rm.Add(`a`, `banner=c`))
	assert.Equal(t, 2, rm.<PERSON>ze())
	assert.True(t, rm.Add(`b`, `banner=c`))
	assert.Equal(t, 3, rm.<PERSON>ze())

	rm.Clear()
	assert.Equal(t, 0, rm.<PERSON>ze())
}

func TestRuleManager_ProductsOfJson(t *testing.T) {
	rm := &ruleManager{}
	rm.Add(`aaa`, `header="X-Auth-Token"`)
	obj, err := structs.NewJsonObj(`{
  "host": "************:19999",
  "ip": "************",
  "port": "19999",
  "header": "HTTP/1.1 200 OK\r\nConnection: close\r\nContent-Length: 5683\r\nAccess-Control-Allow-Headers: Origin, Content-Type, X-Auth-Token\r\nAccess-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS\r\nAccess-Control-Allow-Origin: *\r\nCache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0\r\nCache-Control: no-cache\r\nContent-Type: text/html; charset=UTF-8\r\nDate: Tue, 07 Dec 2021 08:38:44 GMT\r\nExpires: Thu, 19 Nov 1981 08:52:00 GMT\r\nPragma: no-cache\r\nServer: Apache/2.4.18 (Ubuntu)\r\nSet-Cookie: PHPSESSID=4qijsihdtm0f5i2g9sp0b3epm1; path=/\r\nSet-Cookie: laravel_session=eyJpdiI6IlFFYkxcL2RwM0FEaCtVVUI1Y3pyOGxBPT0iLCJ2YWx1ZSI6IjhRSXhyN3NSYlhqV1llMFwvZVJUQXA0WnlwVDhRc3lRQXVNXC9ZSlVvRERhVHNFXC8yTEk1WTE4ZWs4UjR5K3hEYjFaRVlRZ3laSWZXUmlicHlDS0VCbXNRPT0iLCJtYWMiOiIxYTc2NGQ2ZTgyNGIzMzc3OThiMDhkNTgxNmIxNWZlOWNhNTQ2ZjkxN2QxMzYxYjZlYzhlZGY3ZDI0N2ZkZDQ1In0%3D; expires=Tue, 07-Dec-2021 10:38:44 GMT; Max-Age=7200; path=/; httponly\r\nVary: Accept-Encoding\r\n",
  "body": "<!DOCTYPE html>\n<html>\n\n<head>\n\n    <meta charset=\"utf-8\">\n    <meta name=\"csrf-token\" content=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\" />\n    <title>SISTER</title>\n    <link rel=\"icon\" type=\"image/png\" href=\"http://************:19999/favicon.png\"/>\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    <title>INSPINIA | Login 2</title>\n\n    <link href=\"http://************:19999/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/font-awesome/css/font-awesome.min.css\" rel=\"stylesheet\">\n    <!-- <link href=\"http://************:19999/css/animate.css\" rel=\"stylesheet\"> -->\n    <link rel=\"stylesheet\" href=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.css\">\n    <script type=\"text/javascript\" src=\"http://************:19999/js/jquery-2.1.1.js\"></script>\n\n    <link href=\"http://************:19999/css/plugins/toastr/toastr.min.css\" rel=\"stylesheet\">\n\t<script src=\"http://************:19999/js/plugins/toastr/toastr.min.js\"></script>\n\n\t<style type=\"text/css\">\n\t\t#toast-container > .toast-error:before{\n\t\t\tcontent: '';\n\t\t}\n\t</style>\n\t\n\t<script type=\"text/javascript\">\n\t\t$(function () {\n\t\t\ttoastr.options = {\n\t\t\t  \"closeButton\": true,\n\t\t\t  \"debug\": false,\n\t\t\t  \"progressBar\": true,\n\t\t\t  \"preventDuplicates\": false,\n\t\t\t  \"positionClass\": \"toast-top-right\",\n\t\t\t  \"onclick\": null,\n\t\t\t  \"showDuration\": 400,\n\t\t\t  \"hideDuration\": 1000,\n\t\t\t  \"timeOut\": 7000,\n\t\t\t  \"extendedTimeOut\": 1000,\n\t\t\t  \"showEasing\": \"swing\",\n\t\t\t  \"hideEasing\": \"linear\",\n\t\t\t  \"showMethod\": \"fadeIn\",\n\t\t\t  \"hideMethod\": \"fadeOut\"\n\t\t\t}\n\t\t});\n\t</script>\n    <link href=\"http://************:19999/css/plugins/iCheck/custom.css\" rel=\"stylesheet\">\n<script src=\"http://************:19999/js/plugins/iCheck/icheck.min.js\"></script>\n\n<script type=\"text/javascript\">\n\tvar initialize_icheck = function(){\n\t\t$('.i-checks').iCheck({\n            checkboxClass: 'icheckbox_square-green',\n            radioClass: 'iradio_square-green',\n        });\n\t}\n\n    $(document).ready(function () {\n    \tinitialize_icheck();    \n    });\n</script>    \n\n    <link href=\"http://************:19999/eak/ejs/css/classy.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/style.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/login.css\" rel=\"stylesheet\">\n\n\n</head>\n\n<body class=\"gray-bg bg-cover\" style=\"background: url(http://************:19999/bg_login_new.jpg) no-repeat fixed;;\">\n\n    \n    <div class=\"middle-box animated fadeInDown\">\n        <div class=\"abs-bg\"></div>\n\n        <div class=\"content loginscreen\">\n            <div class=\"text-center\">\n                <div>\n                    <img src=\"http://************:19999/logo.png\" class=\"logo\" />\n                </div>\n                <p class=\"new-logo-name\" style=\"margin: 0px\">\n                    SISTER<br>\n                    <p style=\"font-size: small;\"><b>Sistem Informasi Sumberdaya Terintegrasi</b></p>\n                </p>\n                                    <h3><b>IAIN Ambon</b></h3>\n                            </div>\n\n                <form method=\"post\" action=\"http://************:19999/auth/login\" class=\"m-t\" role=\"form\">\n        <input type=\"hidden\" name=\"_token\" value=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\">\n        \n        <div class=\"form-group \">\n            <label class=\"control-label\">USERNAME</label>\n            <input type=\"text\" name=\"username\" value=\"\" class=\"form-control\" placeholder=\"Tulis username/email anda...\">\n        </div>\n        <div class=\"form-group  \">\n            <label class=\"control-label\">PASSWORD</label>\n            <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Tulis password anda...\">\n        </div>\n        \n        <button type=\"submit\" class=\"btn btn-success block full-width m-b noborder-radius\"><b>LOGIN</b></button>\n\n        <table class=\"table\">\n            <tbody>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\">\n                            <label class=\"control-label\">\n                                <h5><a href=\"http://************:19999/password/lupa\">Lupa password?</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                    <td>\n                        <div style=\"text-align: right;\">\n                            <label class=\"control-label\">\n                                <h5>Belum memiliki akun?<a href=\"http://************:19999/registrasi\"> Daftar di sini.</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                </tr>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\"><h4><a href=\"http://************:19999/panduan\" target=\"_blank\" ><i class=\"fa fa-download\"></i> Unduh Panduan</a></h4></td>\n                            </div>\n                    <td><div style=\"text-align: right;\">\n                        <span style=\"font-size: 0.7em\">\n                    <i class=\"fa fa-envelope\"></i>\n                </span>\n                <span style=\"font-size: 0.75em\">\n                    <EMAIL>\n                </span>\n                    </div></td>\n                </tr>\n            </tbody>\n        </table>\n    </form>\n\n            <p class=\"m-t text-center\"> \n                <small>\n                    <b>Direktorat Sumber Daya - Direktorat Jenderal Pendidikan Tinggi, Riset dan Teknologi</b>\n                </small>\n            </p>\n        </div>\n    </div>\n    <script src=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.min.js\"></script>\n</body>\n\n</html>\n",
  "nhash": "-4246898293106848714",
  "ehash": "0aa2f3630eda43fe20d7ef9458e0bea5",
  "lastupdatetime": "2021-12-07 07:40:42"
}`)
	assert.Nil(t, err)
	ps, err := rm.ProductsOfJson(obj)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(ps))
	assert.Equal(t, "aaa", ps[0])

	rm.Clear()
	assert.Equal(t, 0, rm.Size())
	ps, err = rm.ProductsOfJson(obj)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(ps))
}
