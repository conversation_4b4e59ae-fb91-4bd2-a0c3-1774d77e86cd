package model

import (
	"encoding/json"
	"git.gobies.org/shared-platform/foscan/pkg/constant"
	pb "git.gobies.org/shared-platform/foscan/pkg/proto"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type Task struct {
	ID             string  `gorm:"primaryKey"` // 任务ID
	WorkingOn      string  // 正在处理的
	Progress       float32 // 当前进度
	State          string  // 当前状态
	CurrentProcess string  // 当前的处理进程
	Data           string  // 任务内容
}

type Job struct {
	ID        string  `gorm:"primaryKey"` // 任务ID
	WorkingOn string  // 正在处理的
	Progress  float32 // 当前进度
	State     string  // 当前状态
	Data      string  // 任务数据
	NodeID    string  // 节点ID
	TaskID    string  // 父任务
	Task      Task
}

type DBHelper struct {
	db *gorm.DB
}

func NewDBHelper(dbName string) *DBHelper {
	db, err := gorm.Open(sqlite.Open(dbName), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	_ = db.AutoMigrate(&Task{}, &Job{})
	return &DBHelper{
		db: db,
	}
}

func (h *DBHelper) CreateTask(taskID string, data *pb.DispatcherTaskStartRequest) error {
	b, err := json.Marshal(data)
	if err != nil {
		return err
	}
	ret := h.db.Create(&Task{ID: taskID, Data: string(b), State: constant.StateInitial, CurrentProcess: constant.ServicePortScanner})
	return ret.Error
}

func (h *DBHelper) GetTask(taskID string) (*Task, error) {
	var task Task
	ret := h.db.First(&task, "ID=?", taskID)
	return &task, ret.Error
}

func (h *DBHelper) GetUnfinishedTasks() ([]Task, error) {
	var tasks []Task
	ret := h.db.Where("State<>?", constant.StateFinished).Find(&tasks)
	return tasks, ret.Error
}

func (h *DBHelper) UpdateTaskState(taskID, state string) error {
	ret := h.db.Model(&Task{ID: taskID}).Update("State", state)
	return ret.Error
}

func (h *DBHelper) UpdateTaskCurrentProgress(taskID, currentProgress string) error {
	ret := h.db.Model(&Task{ID: taskID}).Update("CurrentProcess", currentProgress)
	return ret.Error
}

func (h *DBHelper) DeleteTask(taskID string) error {
	ret := h.db.Delete(&Task{}, "ID=?", taskID)
	return ret.Error
}

func (h *DBHelper) CreateJob(taskID, jobID string, data *pb.TaskEvent, nodeID string) error {
	b, err := json.Marshal(data)
	if err != nil {
		return err
	}
	ret := h.db.Create(&Job{ID: jobID, Data: string(b), State: constant.StateInitial, TaskID: taskID, NodeID: nodeID})
	return ret.Error
}

func (h *DBHelper) GetJob(jobID string) (*Job, error) {
	var job Job
	ret := h.db.First(&job, "ID=?", jobID)
	return &job, ret.Error
}

func (h *DBHelper) GetJobs(taskID string) ([]Job, error) {
	var jobs []Job
	ret := h.db.Where("Task_ID=?", taskID).Find(&jobs)
	return jobs, ret.Error
}

func (h *DBHelper) UpdateJobState(jobID, state string, progress float32) error {
	ret := h.db.Model(&Job{ID: jobID}).Updates(Job{State: state, Progress: progress})
	return ret.Error
}

func (h *DBHelper) DeleteJob(jobID string) error {
	ret := h.db.Delete(&Job{}, "ID=?", jobID)
	return ret.Error
}
