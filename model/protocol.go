package model

type Protocol struct {
	// ID
	// Required: true
	// Example: test_UDP
	ID string `json:"id" binding:"required"`

	// 名称
	// Required: true
	// Example: test_UDP
	Name string `json:"name" binding:"required"`
	// 协议类型
	// Required: true
	// Example: tcp
	ProtocolType string `json:"protocol_type" binding:"required"`
	// 绑定端口
	// Required: true
	// Example: 10,11,21,22
	BindPort string `json:"bind_port" binding:"required"`
	// 超时时间
	// Required: true
	// Example: 10
	Timeout uint `json:"timeout" binding:"required"`
	// 发包规则
	// Required: true
	Judge []RawGrabRuleJudge `json:"judge" binding:"required"`
}

// RawGrabRuleJudge 规则判定发包
type RawGrabRuleJudge struct {
	Logic       []RawGrabRuleJudgeLogic `json:"logic" binding:"required"`
	SendContent string                  `json:"send_content" binding:"required"`
}

// RawGrabRuleJudgeLogic 规则判定逻辑
type RawGrabRuleJudgeLogic struct {
	Value        string                       `json:"value" binding:"required"`
	Content      string                       `json:"content" binding:"required"`
	LogicType    string                       `json:"logic_type" binding:"required"`
	NextRelation string                       `json:"next_relation" binding:"required"`
	Child        []RawGrabRuleJudgeChildLogic `json:"child" binding:"required"`
}

type RawGrabRuleJudgeChildLogic struct {
	Position string `json:"position" binding:"required"`
	Content  string `json:"content" binding:"required"`
	Value    string `json:"value" binding:"required"`
}
